import { <PERSON>, Row, Col, Typography, Statistic } from "antd";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { useEffect, useState } from "react";
import dayjs from "dayjs";
import { apiCall, endpoints } from "@/lib/api";

import { DateRangePickerWithPresets } from "../common/DateRangePickerWithPresets";
const { Title } = Typography;

interface TopCustomer {
  user_id: number;
  first_name: string;
  email: string;
  total_orders: number;
  total_revenue: number;
  is_new_customer: boolean;
}

interface TopCustomersData {
  start_date: string;
  end_date: string;
  top_customers: TopCustomer[];
}

const COLORS = {
  new: "#52c41a" as const,
  returning: "#1677ff" as const,
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const customer = payload[0].payload as TopCustomer;
    return (
      <div className="bg-white p-3 border rounded-lg shadow-lg">
        <p className="text-sm font-bold">{customer.first_name}</p>
        <p className="text-sm">Tổng đơn hàng: {customer.total_orders}</p>
        <p className="text-sm">
          Doanh thu:{" "}
          {new Intl.NumberFormat("vi-VN", {
            maximumFractionDigits: 0,
          }).format(customer.total_revenue)}{" "}
          ₫
        </p>
        <p className="text-sm">
          {customer.is_new_customer ? "Khách hàng mới" : "Khách hàng cũ"}
        </p>
      </div>
    );
  }
  return null;
};

export function TopCustomerBarChart() {
  const [data, setData] = useState<TopCustomersData | null>(null);

  const fetchData = async (startDate: string, endDate: string) => {
    try {
      const response = await apiCall<TopCustomersData>(
        "GET",
        endpoints.reports.topCustomer +
          `?start_date=${startDate}&end_date=${endDate}`
      );
      console.log("Top Customers Data:", response);
      setData(response);
    } catch (error) {
      console.error("Error fetching top customers data:", error);
    }
  };

  useEffect(() => {
    const today = dayjs();
    const startDate = today.subtract(30, "day").format("YYYY-MM-DD");
    const endDate = today.format("YYYY-MM-DD");
    console.log("Fetching data from:", startDate, "to:", endDate);
    fetchData(startDate, endDate);
  }, []);

  const handleDateChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      const startDate = dates[0].format("YYYY-MM-DD");
      const endDate = dates[1].format("YYYY-MM-DD");
      fetchData(startDate, endDate);
    }
  };

  return (
    <Card>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={8}>
          <Statistic
            title="Tổng Doanh Thu Top 10"
            value={
              data?.top_customers?.reduce(
                (sum, customer) => sum + customer.total_revenue,
                0
              ) || 0
            }
            precision={0}
            formatter={(value) =>
              new Intl.NumberFormat("vi-VN", {
                maximumFractionDigits: 0,
              }).format(Number(value)) + " ₫"
            }
          />
        </Col>
        <Col xs={24} md={16}>
          <Row justify="end">
            <DateRangePickerWithPresets
              className="justify-end"
              onChange={(dates) => {
                if (dates[0] && dates[1]) {
                  fetchData(dates[0], dates[1]);
                }
              }}
            />
          </Row>
        </Col>
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Row gutter={[0, 16]}>
                {data && (
                  <>
                    <Col span={24} style={{ marginBottom: 8 }}>
                      <Typography.Text type="secondary">
                        Thời gian: {dayjs(data.start_date).format("DD/MM/YYYY")}{" "}
                        - {dayjs(data.end_date).format("DD/MM/YYYY")}
                      </Typography.Text>
                    </Col>
                    <Col span={24}>
                      <Title level={5}>Chi tiết khách hàng:</Title>
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 12,
                        }}
                      >
                        <div>
                          <span
                            style={{
                              display: "inline-block",
                              width: 12,
                              height: 12,
                              backgroundColor: COLORS.new,
                              marginRight: 8,
                            }}
                          ></span>
                          <span style={{ fontSize: "1.1em" }}>
                            Khách hàng mới:{" "}
                            {
                              data.top_customers.filter(
                                (c) => c.is_new_customer
                              ).length
                            }{" "}
                            khách -{" "}
                            {new Intl.NumberFormat("vi-VN", {
                              maximumFractionDigits: 0,
                            }).format(
                              data.top_customers
                                .filter((c) => c.is_new_customer)
                                .reduce((sum, c) => sum + c.total_revenue, 0)
                            )}{" "}
                            ₫
                          </span>
                        </div>
                        <div>
                          <span
                            style={{
                              display: "inline-block",
                              width: 12,
                              height: 12,
                              backgroundColor: COLORS.returning,
                              marginRight: 8,
                            }}
                          ></span>
                          <span style={{ fontSize: "1.1em" }}>
                            Khách hàng cũ:{" "}
                            {
                              data.top_customers.filter(
                                (c) => !c.is_new_customer
                              ).length
                            }{" "}
                            khách -{" "}
                            {new Intl.NumberFormat("vi-VN", {
                              maximumFractionDigits: 0,
                            }).format(
                              data.top_customers
                                .filter((c) => !c.is_new_customer)
                                .reduce((sum, c) => sum + c.total_revenue, 0)
                            )}{" "}
                            ₫
                          </span>
                        </div>
                      </div>
                    </Col>
                  </>
                )}
              </Row>
            </Col>
            <Col xs={24} md={16}>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={data?.top_customers || []}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" horizontal={true} />
                  <XAxis
                    type="number"
                    tickFormatter={(value) =>
                      `${value.toLocaleString("vi-VN")} ₫`
                    }
                  />
                  <YAxis type="category" dataKey="first_name" width={200} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="total_revenue"
                    fill={COLORS.returning}
                    stroke={COLORS.returning}
                    fillOpacity={0.8}
                  >
                    {data?.top_customers?.map(
                      (entry: TopCustomer, index: number) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.is_new_customer
                              ? COLORS.new
                              : COLORS.returning
                          }
                        />
                      )
                    ) || []}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </Col>
          </Row>
        </Col>
      </Row>
    </Card>
  );
}
