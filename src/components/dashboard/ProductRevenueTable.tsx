import { useState, useEffect } from "react";
import { ProductRevenueResponse, ProductRevenueItem } from "@/types/report";
import {
  getProductRevenue,
  ProductRevenueParams,
  apiCall,
  endpoints,
} from "@/lib/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CategoryFilter } from "@/components/products/CategoryFilter";
import dayjs from "dayjs";
import { DateRangePickerWithPresets } from "@/components/common/DateRangePickerWithPresets";
import { cn } from "@/lib/utils";
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";
import { useAuth } from "@/context/auth-hooks";
import { useSearchParams } from "react-router-dom";

const PAGE_SIZE = 50;

type SortField = "total_quantity_sold" | "total_revenue";
type SortOrder = "asc" | "desc";

interface SortConfig {
  field: SortField;
  order: SortOrder;
}

interface ProductRevenueTableProps {
  revenueType?: "actual" | "all";
}

export function ProductRevenueTable({
  revenueType = "actual",
}: ProductRevenueTableProps) {
  const { user } = useAuth();
  const [searchParams] = useSearchParams();

  const [data, setData] = useState<ProductRevenueResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<[string | null, string | null]>([
    dayjs().startOf("month").format("YYYY-MM-DD"),
    dayjs().format("YYYY-MM-DD"),
  ]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [sort, setSort] = useState<SortConfig>({
    field: "total_revenue",
    order: "desc",
  });

  const fetchData = async () => {
    try {
      setLoading(true);
      const params: ProductRevenueParams = {
        page_size: PAGE_SIZE,
      };

      if (dateRange[0]) {
        params.dateFrom = dateRange[0];
      }
      if (dateRange[1]) {
        params.dateTo = dateRange[1];
      }
      if (selectedCategory) {
        params.category = selectedCategory;
      }

      // Get sales_admin from URL params
      const salesAdminId = searchParams.get("sales_admin");
      if (salesAdminId) {
        params.sales_admin = Number(salesAdminId);
      } else if (user?.role === "sales_admin") {
        // If user is sales_admin, use their ID
        params.sales_admin = user.id;
      }

      let response;

      // Use the updated getProductRevenue function with the includeAllOrders parameter
      response = await getProductRevenue(params, revenueType === "all");

      const sortedResults = [...response.results].sort((a, b) => {
        const multiplier = sort.order === "desc" ? -1 : 1;
        return (a[sort.field] - b[sort.field]) * multiplier;
      });

      setData({
        ...response,
        results: sortedResults,
      });
    } catch (error) {
      console.error("Error fetching product revenue:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange, selectedCategory, revenueType, searchParams]);

  const handleSort = (field: SortField) => {
    const newSort: SortConfig = {
      field,
      order:
        sort.field === field
          ? sort.order === "desc"
            ? "asc"
            : "desc"
          : "desc",
    };
    setSort(newSort);

    if (data) {
      const sortedResults = [...data.results].sort((a, b) => {
        const multiplier = newSort.order === "desc" ? -1 : 1;
        return (a[field] - b[field]) * multiplier;
      });
      setData({
        ...data,
        results: sortedResults,
      });
    }
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sort.field !== field) return null;
    return sort.order === "desc" ? (
      <ArrowDownIcon className="h-4 w-4 inline ml-1" />
    ) : (
      <ArrowUpIcon className="h-4 w-4 inline ml-1" />
    );
  };

  if (loading) return <div>Đang tải...</div>;

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <DateRangePickerWithPresets
          value={dateRange}
          onChange={(dates) =>
            setDateRange([dates[0] || null, dates[1] || null])
          }
        />
        <CategoryFilter
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Mã hàng</TableHead>
              <TableHead>Tên sản phẩm</TableHead>
              {user?.role === "sales_manager" && (
                <>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("total_revenue")}
                  >
                    Tổng doanh thu
                    <SortIcon field="total_revenue" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("total_quantity_sold")}
                  >
                    Số lượng bán
                    <SortIcon field="total_quantity_sold" />
                  </TableHead>
                </>
              )}
              <TableHead>Tồn kho</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.results.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.code}</TableCell>
                <TableCell>{item.name}</TableCell>
                {user?.role === "sales_manager" && (
                  <>
                    <TableCell>
                      {new Intl.NumberFormat("vi-VN", {
                        style: "currency",
                        currency: "VND",
                      }).format(item.total_revenue)}
                    </TableCell>
                    <TableCell>{item.total_quantity_sold}</TableCell>
                  </>
                )}
                <TableCell>{item.stock}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
