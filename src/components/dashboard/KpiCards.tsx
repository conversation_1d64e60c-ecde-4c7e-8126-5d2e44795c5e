import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import {
  BanknotesIcon,
  ShoppingCartIcon,
  ChartBarIcon,
  ClipboardDocumentCheckIcon,
} from "@heroicons/react/24/outline";

interface KpiCardsProps {
  totalRevenue: number;
  totalOrders: number;
  avgOrderValue: number;
  processingOrders: number;
  isLoading?: boolean;
}

export function KpiCards({
  totalRevenue,
  totalOrders,
  avgOrderValue,
  processingOrders,
  isLoading,
}: KpiCardsProps) {
  const cards = [
    {
      title: "Doanh Thu",
      value: formatCurrency(totalRevenue),
      icon: BanknotesIcon,
      className: "text-green-500",
    },
    {
      title: "Tổng Đơn Hàng",
      value: totalOrders.toLocaleString("vi-VN"),
      icon: ShoppingCartIcon,
      className: "text-blue-500",
    },
    {
      title: "<PERSON><PERSON><PERSON> TB Đơn <PERSON>",
      value: formatCurrency(avgOrderValue),
      icon: ChartBarIcon,
      className: "text-purple-500",
    },
    {
      title: "Đơn Đang Xử Lý",
      value: processingOrders.toLocaleString("vi-VN"),
      icon: ClipboardDocumentCheckIcon,
      className: "text-orange-500",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card) => (
        <Card key={card.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
            <card.icon className={`h-4 w-4 ${card.className}`} />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="text-2xl font-bold">{card.value}</div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
