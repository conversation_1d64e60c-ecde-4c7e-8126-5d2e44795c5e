import { ReactNode, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../context/auth-hooks";
import { Layout, Menu, theme } from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  DashboardOutlined,
  GiftOutlined,
  TableOutlined,
  ProjectOutlined,
  Bar<PERSON>hartOutlined,
  DollarOutlined,
  LogoutOutlined,
} from "@ant-design/icons";
import { useIsMobile } from "../../hooks/useBreakpoint";
import { Footer } from "./Footer";

const { Header, Sider, Content } = Layout;

interface AdminLayoutProps {
  children: ReactNode;
}

const getMenuItems = (role?: string) => {
  // Define orders menu children based on role
  const getOrdersChildren = (role?: string) => {
    const baseChildren = [
      {
        key: "/orders",
        icon: <TableOutlined />,
        label: "Table",
      },
      {
        key: "/orders/showroom",
        icon: <ShoppingCartOutlined />,
        label: "Showroom",
      },
    ];

    // Add Kanban view for sales_manager and warehouse_staff
    if (role === "sales_manager" || role === "warehouse_staff") {
      baseChildren.push({
        key: "/orders/kanban",
        icon: <ProjectOutlined />,
        label: "Kanban",
      });
    }

    return baseChildren;
  };

  // Base items with dynamic orders children
  const baseItems = [
    {
      key: "/",
      icon: <DashboardOutlined />,
      label: "Trang chủ",
    },
    {
      key: "report",
      icon: <BarChartOutlined />,
      label: "Phân tích",
      children: [
        {
          key: "/reports/customer",
          icon: <UserOutlined />,
          label: "Khách hàng",
        },
        {
          key: "/reports/revenue",
          icon: <DollarOutlined />,
          label: "Doanh thu",
        },
        {
          key: "/reports/products",
          icon: <GiftOutlined />,
          label: "Sản phẩm",
        },
        {
          key: "/reports/delivery",
          icon: <DollarOutlined />,
          label: "Doanh thu vận chuyển",
        },
        {
          key: "/reports/top-customers",
          icon: <UserOutlined />,
          label: "Top khách hàng",
        },
      ],
    },
    {
      key: "orders",
      icon: <ShoppingCartOutlined />,
      label: "Đơn hàng",
      children: getOrdersChildren(role),
    },
    {
      key: "/customers",
      icon: <UserOutlined />,
      label: "Khách hàng",
    },
    {
      key: "/products",
      icon: <GiftOutlined />,
      label: "Sản phẩm",
    },
    {
      key: "/staff",
      icon: <UserOutlined />,
      label: "Nhân Viên",
    },
  ];

  // Filter menu items based on role
  switch (role) {
    case "sales_manager":
      return baseItems;
    case "sales_admin":
      return baseItems.filter(
        (item) => item.key !== "/staff" && item.key !== "report"
      );
    case "delivery_staff":
    case "warehouse_staff":
      return baseItems.filter((item) => item.key === "orders");
    default:
      return [];
  }
};

export function AdminLayout({ children }: AdminLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { token } = theme.useToken();
  const menuItems = getMenuItems(user?.role);
  const isMobile = useIsMobile();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const userRole = user?.is_superuser ? "Admin" : user?.is_staff ? "Staff" : "";

  const onMenuClick = (item: any) => {
    navigate(item.key);
  };

  return (
    <Layout className="min-h-screen">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        breakpoint="md"
        className="overflow-auto h-screen fixed left-0 top-0 bottom-0"
        style={{
          backgroundColor: token.colorBgContainer,
        }}
      >
        <div
          className={`h-16 flex items-center ${
            collapsed ? "justify-center" : "justify-between px-4"
          } border-b border-solid`}
          style={{
            borderColor: token.colorBorderSecondary,
          }}
        >
          {!collapsed && <h2 className="m-0 text-xl font-bold">3T Admin</h2>}
          <button
            onClick={() => setCollapsed(!collapsed)}
            className="border-0 bg-transparent p-2 cursor-pointer hover:bg-gray-100 rounded-lg"
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </button>
        </div>
        <Menu
          mode="inline"
          defaultOpenKeys={["orders"]}
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={onMenuClick}
          className="border-0"
        />
      </Sider>
      <Layout
        className="transition-all duration-200"
        style={{ marginLeft: collapsed ? 80 : 200 }}
      >
        <Header
          className="px-4 sticky top-0 z-10 w-full flex items-center justify-end border-b border-solid"
          style={{
            background: token.colorBgContainer,
            borderColor: token.colorBorderSecondary,
          }}
        >
          {user && (
            <>
              {!isMobile && (
                <div className="flex flex-col items-end mr-4">
                  <span className="text-sm">{user.email}</span>
                  <span
                    className="text-xs"
                    style={{ color: token.colorTextSecondary }}
                  >
                    {userRole}
                  </span>
                </div>
              )}
              <button
                onClick={handleLogout}
                className="border-0 bg-transparent cursor-pointer hover:opacity-80"
                style={{ color: token.colorPrimary }}
              >
                {isMobile ? (
                  <LogoutOutlined className="text-xl" />
                ) : (
                  "Đăng xuất"
                )}
              </button>
            </>
          )}
        </Header>
        <Content className="p-6 min-h-[280px]">{children}</Content>
        <Footer />
      </Layout>
    </Layout>
  );
}
