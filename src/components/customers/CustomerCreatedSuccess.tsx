import { CreatedCustomerResponse } from "../../types/customer";
import { useToast } from "../../context/toast-hooks";

interface CustomerCreatedSuccessProps {
  customer: CreatedCustomerResponse;
  onViewCustomer: () => void;
  onCreateAnother: () => void;
}

export function CustomerCreatedSuccess({
  customer,
  onViewCustomer,
  onCreateAnother,
}: CustomerCreatedSuccessProps) {
  const { showToast } = useToast();

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-green-800 mb-4">
          Tạo khách hàng thành công!
        </h2>
        <div className="space-y-3">
          <div className="space-y-1">
            <div className="text-sm">
              <span className="font-medium">Email:</span> {customer.user.email}
            </div>
            <div className="text-sm">
              <span className="font-medium">Họ tên:</span>{" "}
              {customer.user.first_name} {customer.user.last_name}
            </div>
            <div className="text-sm">
              <span className="font-medium">Số điện thoại:</span>{" "}
              {customer.user.phone_number}
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
            <p className="font-medium text-yellow-800 mb-1">
              Thông tin đăng nhập
            </p>
            <div className="text-sm space-y-1">
              <p>
                <span className="font-medium">Tên đăng nhập:</span>{" "}
                {customer.user.email}
              </p>
              <p>
                <span className="font-medium">Mật khẩu:</span>{" "}
                <code className="bg-yellow-100 px-1 py-0.5 rounded">
                  {customer.user.password}
                </code>
              </p>
            </div>
            <div className="flex items-center mt-2 text-sm text-yellow-700">
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p>Thông tin đăng nhập đã được gửi đến email khách hàng</p>
            </div>
          </div>

          <button
            onClick={() => {
              navigator.clipboard.writeText(
                `Email: ${customer.user.email}\nMật khẩu: ${customer.user.password}`
              );
              showToast("Đã sao chép thông tin đăng nhập", "success");
            }}
            className="w-full border border-gray-300 text-sm rounded p-2 hover:bg-gray-50 mt-2"
          >
            Sao chép thông tin đăng nhập
          </button>
        </div>
      </div>

      <div className="flex gap-4">
        <button
          onClick={onViewCustomer}
          className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90"
        >
          Xem khách hàng
        </button>
        <button
          onClick={onCreateAnother}
          className="border px-4 py-2 rounded hover:bg-gray-50"
        >
          Tạo khách hàng khác
        </button>
      </div>
    </div>
  );
}
