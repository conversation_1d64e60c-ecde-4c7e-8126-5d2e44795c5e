import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { CustomerNote, CreateNoteData } from "../../types/customer";
import { apiCall, endpoints } from "../../lib/api";
import { useToast } from "../../context/toast-hooks";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const noteSchema = z.object({
  content: z
    .string()
    .min(1, "Nội dung ghi chú không được để trống")
    .max(1000, "Nội dung ghi chú không được quá 1000 ký tự"),
  is_pinned: z.boolean().default(false),
});

type NoteFormData = z.infer<typeof noteSchema>;

interface CustomerNotesProps {
  customerId: number;
}

export function CustomerNotes({ customerId }: CustomerNotesProps) {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<NoteFormData>({
    resolver: zodResolver(noteSchema),
    defaultValues: {
      content: "",
      is_pinned: false,
    },
  });

  const { data: notes, isLoading } = useQuery<CustomerNote[]>({
    queryKey: ["customer-notes", customerId],
    queryFn: () =>
      apiCall("GET", endpoints.customers.notes(customerId)),
  });

  const createNote = useMutation({
    mutationFn: (data: CreateNoteData) =>
      apiCall("POST", endpoints.customers.notes(customerId), data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-notes", customerId] });
      showToast("Đã thêm ghi chú", "success");
      reset();
    },
    onError: () => {
      showToast("Không thể thêm ghi chú", "error");
    },
  });

  const deleteNote = useMutation({
    mutationFn: (noteId: number) =>
      apiCall(
        "DELETE",
        `${endpoints.customers.notes(customerId)}/${noteId}`
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-notes", customerId] });
      showToast("Đã xóa ghi chú", "success");
    },
    onError: () => {
      showToast("Không thể xóa ghi chú", "error");
    },
  });

  const togglePin = useMutation({
    mutationFn: ({ noteId, isPinned }: { noteId: number; isPinned: boolean }) =>
      apiCall(
        "PATCH",
        `${endpoints.customers.notes(customerId)}/${noteId}`,
        {
          is_pinned: !isPinned,
        }
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-notes", customerId] });
      showToast("Đã cập nhật ghi chú", "success");
    },
    onError: () => {
      showToast("Không thể cập nhật ghi chú", "error");
    },
  });

  const onSubmit = (data: NoteFormData) => {
    createNote.mutate(data);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const pinnedNotes = notes?.filter((note) => note.is_pinned) || [];
  const unpinnedNotes = notes?.filter((note) => !note.is_pinned) || [];

  return (
    <div className="space-y-6">
      {/* Add Note Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-start gap-4">
            <div className="flex-1">
              <textarea
                {...register("content")}
                rows={3}
                placeholder="Thêm ghi chú..."
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
              {errors.content && (
                <p className="text-xs text-destructive mt-1">
                  {errors.content.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              {/* <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  {...register("is_pinned")}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">Ghim ghi chú</span>
              </label> */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90 disabled:opacity-50"
              >
                Thêm ghi chú
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* Notes List */}
      <div className="space-y-6">
        {/* Pinned Notes */}
        {pinnedNotes.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground">
              Ghi chú đã ghim
            </h4>
            <div className="space-y-2">
              {pinnedNotes.map((note) => (
                <div
                  key={note.id}
                  className="p-4 border rounded-lg bg-muted/30 relative"
                >
                  <div className="flex justify-between items-start gap-4">
                    <div className="flex-1 space-y-1">
                      <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>
                          Bởi {note.created_by?.full_name} vào{" "}
                          {new Date(note.created_at).toLocaleDateString()}
                        </span>
                        {note.updated_at && (
                          <span>
                            (Đã sửa{" "}
                            {new Date(note.updated_at).toLocaleDateString()})
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() =>
                          togglePin.mutate({
                            noteId: note.id,
                            isPinned: note.is_pinned,
                          })
                        }
                        className="p-1 hover:text-primary"
                      >
                        📌
                      </button>
                      <button
                        onClick={() => deleteNote.mutate(note.id)}
                        className="p-1 text-destructive hover:text-destructive/90"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other Notes */}
        {unpinnedNotes.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground">Ghi chú</h4>
            <div className="space-y-2">
              {unpinnedNotes.map((note) => (
                <div key={note.id} className="p-4 border rounded-lg relative">
                  <div className="flex justify-between items-start gap-4">
                    <div className="flex-1 space-y-1">
                      <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>
                          Bởi {note.created_by?.full_name} vào{" "}
                          {new Date(note.created_at).toLocaleDateString()}
                        </span>
                        {note.updated_at && (
                          <span>
                            (Đã sửa{" "}
                            {new Date(note.updated_at).toLocaleDateString()})
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() =>
                          togglePin.mutate({
                            noteId: note.id,
                            isPinned: note.is_pinned,
                          })
                        }
                        className="p-1 text-muted-foreground hover:text-primary"
                      >
                        📌
                      </button>
                      <button
                        onClick={() => deleteNote.mutate(note.id)}
                        className="p-1 text-destructive hover:text-destructive/90"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {notes?.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Chưa có ghi chú nào.
          </div>
        )}
      </div>
    </div>
  );
}
