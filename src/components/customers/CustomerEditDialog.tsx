import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Customer } from "../../types/customer";
import { apiCall, endpoints } from "../../lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "../../context/toast-hooks";
import { customerFormSchema, CustomerFormData } from "../../lib/validations/customer";

interface CustomerEditDialogProps {
  customer: Customer;
  onClose: () => void;
}

export function CustomerEditDialog({ customer, onClose }: CustomerEditDialogProps) {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerFormSchema),
    defaultValues: {
      first_name: customer.first_name,
      last_name: customer.last_name,
      email: customer.email,
      phone_number: customer.profile.phone_number || "",
      shipping_address: customer.profile.shipping_address || "",
      ward: customer.profile.ward || "",
      district: customer.profile.district || "",
      city: customer.profile.city || "",
    },
  });

  const onSubmit = async (data: CustomerFormData) => {
    try {
      await apiCall("PATCH", endpoints.customers.update(customer.id), {
        ...data,
        profile: {
          phone_number: data.phone_number,
          shipping_address: data.shipping_address,
          ward: data.ward,
          district: data.district,
          city: data.city,
        },
      });

      queryClient.invalidateQueries({ type: "all" });

      showToast("Đã cập nhật thông tin khách hàng", "success");
      onClose();
    } catch (error) {
      console.error("Không thể cập nhật thông tin khách hàng:", error);
      showToast("Không thể cập nhật thông tin khách hàng", "error");
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg sm:rounded-lg">
        <div className="flex flex-col space-y-1.5">
          <h2 className="text-lg font-semibold">Sửa thông tin khách hàng</h2>
          <p className="text-sm text-muted-foreground">
            Cập nhật chi tiết và thông tin liên hệ của khách hàng
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Tên</label>
              <input
                {...register("first_name")}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
              {errors.first_name && (
                <p className="text-xs text-destructive mt-1">
                  {errors.first_name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Họ</label>
              <input
                {...register("last_name")}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
              {errors.last_name && (
                <p className="text-xs text-destructive mt-1">
                  {errors.last_name.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Email</label>
            <input
              {...register("email")}
              type="email"
              className="w-full px-3 py-2 border rounded-md text-sm"
            />
            {errors.email && (
              <p className="text-xs text-destructive mt-1">
                {errors.email.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Số điện thoại</label>
            <input
              {...register("phone_number")}
              type="tel"
              className="w-full px-3 py-2 border rounded-md text-sm"
            />
            {errors.phone_number && (
              <p className="text-xs text-destructive mt-1">
                {errors.phone_number.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Địa chỉ giao hàng</label>
            <textarea
              {...register("shipping_address")}
              rows={3}
              className="w-full px-3 py-2 border rounded-md text-sm"
            />
            {errors.shipping_address && (
              <p className="text-xs text-destructive mt-1">
                {errors.shipping_address.message}
              </p>
            )}
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Phường</label>
              <input
                {...register("ward")}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
              {errors.ward && (
                <p className="text-xs text-destructive mt-1">
                  {errors.ward.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Quận</label>
              <input
                {...register("district")}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
              {errors.district && (
                <p className="text-xs text-destructive mt-1">
                  {errors.district.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Thành phố</label>
              <input
                {...register("city")}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
              {errors.city && (
                <p className="text-xs text-destructive mt-1">
                  {errors.city.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border rounded hover:bg-muted"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50"
            >
              {isSubmitting ? "Đang lưu..." : "Lưu thay đổi"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
