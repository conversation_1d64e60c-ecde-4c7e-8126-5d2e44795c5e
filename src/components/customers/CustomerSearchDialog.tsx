import { useState } from "react";
import { Customer } from "../../types/customer";
import { api, endpoints } from "../../lib/api";
import {
  Modal,
  Input,
  Button,
  List,
  Typography,
  Space,
  Empty,
  Card,
  Row,
  Col,
} from "antd";
import {
  SearchOutlined,
  UserAddOutlined,
  EnvironmentOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
} from "@ant-design/icons";

const { Text } = Typography;

interface CustomerSearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (customer: Customer) => void;
  onCreateNew: () => void;
}

export function CustomerSearchDialog({
  isOpen,
  onClose,
  onSelect,
  onCreateNew,
}: CustomerSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [searching, setSearching] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    try {
      setSearching(true);
      const response = await api.get(endpoints.customers.list, {
        params: {
          role: "customer",
          search: searchTerm,
        },
      });
      setCustomers(response.data.results);
    } catch (error) {
      console.error("Không thể tìm kiếm khách hàng:", error);
    } finally {
      setSearching(false);
    }
  };

  // onPressEnter in Input component handles the Enter key press

  const formatAddress = (customer: Customer) => {
    if (!customer.profile.shipping_address) return null;

    let address = customer.profile.shipping_address;
    if (customer.profile.ward) address += `, P.${customer.profile.ward}`;
    if (customer.profile.district)
      address += `, Q.${customer.profile.district}`;
    if (customer.profile.city) address += `, ${customer.profile.city}`;

    return address;
  };

  return (
    <Modal
      title="Chọn khách hàng"
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={800}
      styles={{ body: { padding: "16px" } }}
    >
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Row gutter={16}>
          <Col flex="auto">
            <Input
              placeholder="Tìm kiếm theo tên, email hoặc số điện thoại"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined />}
              size="large"
            />
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                loading={searching}
                onClick={handleSearch}
                size="large"
              >
                {searching ? "Đang tìm..." : "Tìm kiếm"}
              </Button>
              <Button
                type="primary"
                icon={<UserAddOutlined />}
                onClick={onCreateNew}
                style={{ backgroundColor: "#52c41a" }}
                size="large"
              >
                Tạo mới
              </Button>
            </Space>
          </Col>
        </Row>

        {customers.length === 0 ? (
          <Empty
            description="Không tìm thấy khách hàng"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <List
            dataSource={customers}
            renderItem={(customer) => (
              <Card
                key={customer.id}
                hoverable
                style={{ marginBottom: 16 }}
                onClick={() => onSelect(customer)}
              >
                <Row justify="space-between" align="top">
                  <Col>
                    <Space direction="vertical" size="small">
                      <Space align="center">
                        <UserOutlined />
                        <Text strong>
                          {customer.first_name} {customer.last_name}
                        </Text>
                      </Space>

                      <Space align="center">
                        <MailOutlined />
                        <Text type="secondary">{customer.email}</Text>
                      </Space>

                      {customer.profile.phone_number && (
                        <Space align="center">
                          <PhoneOutlined />
                          <Text type="secondary">
                            {customer.profile.phone_number}
                          </Text>
                        </Space>
                      )}

                      {formatAddress(customer) && (
                        <Space align="start">
                          <EnvironmentOutlined style={{ marginTop: 4 }} />
                          <Text type="secondary" style={{ maxWidth: 500 }}>
                            {formatAddress(customer)}
                          </Text>
                        </Space>
                      )}
                    </Space>
                  </Col>

                  <Col>
                    <Button
                      type="primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        onSelect(customer);
                      }}
                    >
                      Chọn
                    </Button>
                  </Col>
                </Row>
              </Card>
            )}
          />
        )}
      </Space>
    </Modal>
  );
}
