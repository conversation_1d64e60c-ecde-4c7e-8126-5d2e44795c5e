import { Form, Input, InputNumber, Switch, Select, Button, Upload } from "antd";
import { CustomUploadFile } from "@/types/upload";
import { PlusOutlined } from "@ant-design/icons";
import { Product, Category, ProductImage } from "@/types/product";
import { useState } from "react";

export type ProductFormData = Omit<
  Product,
  | "id"
  | "images"
  | "variants"
  | "created_at"
  | "updated_at"
  | "main_image"
  | "image"
>;

interface ProductFormProps {
  onFinish: (
    values: ProductFormData,
    images: CustomUploadFile[]
  ) => Promise<void>;
  initialValues?: Partial<ProductFormData>;
  categories: Category[];
  loading?: boolean;
  submitText?: string;
  existingImages?: ProductImage[];
  disabled?: boolean;
}

const ProductForm = ({
  onFinish,
  initialValues,
  categories,
  loading = false,
  submitText = "Lưu",
  existingImages = [],
  disabled = false,
}: ProductFormProps) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<CustomUploadFile[]>(
    existingImages.map((img) => ({
      uid: String(img.id),
      name: img.alt_text || "image",
      status: "done",
      url: img.image,
      is_primary: img.is_primary,
    }))
  );

  const handleFinish = async (values: ProductFormData) => {
    await onFinish(values, fileList);
  };

  const handleUploadChange = ({
    fileList,
  }: {
    fileList: CustomUploadFile[];
  }) => {
    setFileList(fileList);
  };

  const handlePrimaryImageChange = (uid: string) => {
    setFileList(
      fileList.map((file) => ({
        ...file,
        is_primary: file.uid === uid,
      }))
    );
  };

  const formInitialValues = {
    is_featured: false,
    is_active: true,
    ...initialValues,
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
      initialValues={formInitialValues}
      disabled={disabled}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Form.Item
            name="name"
            label="Tên sản phẩm"
            rules={[{ required: true, message: "Vui lòng nhập tên sản phẩm" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item required name="description" label="Mô tả">
            <Input.TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="price"
            label="Giá"
            rules={[{ required: true, message: "Vui lòng nhập giá" }]}
          >
            <InputNumber
              style={{ width: "100%" }}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              parser={(value) => value!.replace(/\$\s?|(,*)/g, "")}
            />
          </Form.Item>

          <Form.Item name="discount_price" label="Giá khuyến mãi">
            <InputNumber
              style={{ width: "100%" }}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              parser={(value) => value!.replace(/\$\s?|(,*)/g, "")}
            />
          </Form.Item>

          <Form.Item name="category" label="Danh mục">
            <Select>
              {categories.map((category) => (
                <Select.Option key={category.id} value={category.id}>
                  {category.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <div className="flex justify-between">
            <Form.Item
              name="is_featured"
              label="Sản phẩm nổi bật"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="is_active"
              label="Đang hoạt động"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </div>
        </div>

        <div>
          <Form.Item
            name="stock"
            label="Tồn kho"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập số lượng tồn kho",
              },
            ]}
          >
            <InputNumber style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item name="weight" label="Khối lượng (kg)">
            <InputNumber style={{ width: "100%" }} step={0.1} />
          </Form.Item>

          <Form.Item
            name="unit"
            label="Đơn vị"
            rules={[{ required: true, message: "Vui lòng nhập đơn vị" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="code"
            label="Mã sản phẩm"
            rules={[{ required: true, message: "Vui lòng nhập mã sản phẩm" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="specifications" label="Thông số kỹ thuật">
            <Input.TextArea rows={4} />
          </Form.Item>

          <Form.Item label="Hình ảnh sản phẩm">
            <Upload
              listType="picture-card"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={() => false}
              multiple
            >
              {fileList.length < 8 && (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>Tải lên</div>
                </div>
              )}
            </Upload>
            {fileList.length > 0 && (
              <div className="mt-4">
                <p className="text-sm font-medium mb-2">Ảnh chính:</p>
                <Select
                  value={fileList.find((f) => f.is_primary)?.uid}
                  onChange={handlePrimaryImageChange}
                  style={{ width: "100%" }}
                >
                  {fileList.map((file) => (
                    <Select.Option key={file.uid} value={file.uid}>
                      {file.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            )}
          </Form.Item>
        </div>
      </div>

      <Form.Item className="flex justify-end">
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          disabled={disabled}
        >
          {submitText}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ProductForm;
