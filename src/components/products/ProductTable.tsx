import { Table, Button, Tag, Image } from "antd";
import type { TableProps } from "antd";
import { useNavigate } from "react-router-dom";
import { ProductListItem } from "../../types/product";
import { formatCurrency } from "../../lib/utils";

interface ProductTableProps {
  data: ProductListItem[];
  loading?: boolean;
}

export function ProductTable({ data, loading }: ProductTableProps) {
  const navigate = useNavigate();
  const columns: TableProps<ProductListItem>["columns"] = [
    {
      title: "Sản phẩm",
      key: "product",
      render: (_, record) => (
        <div className="flex items-center gap-3">
          <Image
            src={record.main_image}
            alt={record.name}
            width={40}
            height={40}
            className="object-cover rounded"
            preview={false}
          />
          <div>
            <div className="font-medium">{record.name}</div>
            <div className="text-sm text-muted-foreground">
              {record.code || "Chưa có mã"}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Danh mục",
      key: "category",
      render: (_, record) => (
        <div className="text-sm text-muted-foreground">
          {record.category_name ? record.category_name : "Chưa có danh mục"}
        </div>
      ),
    },
    {
      title: "Giá",
      key: "price",
      render: (_, record) => (
        <div>
          {record.discount_price ? (
            <>
              <div className="text-destructive line-through">
                {formatCurrency(record.price)}
              </div>
              <div>{formatCurrency(record.discount_price)}</div>
            </>
          ) : (
            formatCurrency(record.price)
          )}
        </div>
      ),
    },
    {
      title: "Tồn kho",
      dataIndex: "stock",
      key: "stock",
      render: (stock) => (
        <Tag color={stock > 0 ? "success" : "error"}>{stock}</Tag>
      ),
    },
    {
      title: "Trạng thái",
      key: "status",
      render: (_, record) => (
        <Tag color={record.is_active ? "success" : "error"}>
          {record.is_active ? "Đang bán" : "Tạm ngừng"}
        </Tag>
      ),
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Button
          type="link"
          className="text-primary hover:text-primary/90"
          onClick={() => navigate(`/products/${record.id}`)}
        >
          Sửa
        </Button>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      pagination={false}
      rowClassName={(_, index) => (index % 2 === 0 ? "bg-white" : "bg-gray-50")}
    />
  );
}
