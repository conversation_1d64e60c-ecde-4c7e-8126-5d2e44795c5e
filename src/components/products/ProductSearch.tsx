import { useState } from "react";
import { Button, Input, Checkbox, Space } from "antd";
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons";
import { CategoryFilter } from "./CategoryFilter";

interface ProductSearchProps {
  onSearch: (params: ProductSearchParams) => void;
}

export interface ProductSearchParams {
  query?: string;
  categoryId?: number | null;
  inStock?: boolean;
  isActive?: boolean;
}

export function ProductSearch({ onSearch }: ProductSearchProps) {
  const [searchParams, setSearchParams] = useState<ProductSearchParams>({
    categoryId: null,
  });

  const handleSearch = () => {
    onSearch(searchParams);
  };

  const handleReset = () => {
    const resetParams = { categoryId: null };
    setSearchParams(resetParams);
    onSearch(resetParams);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm sản phẩm..."
              value={searchParams.query || ""}
              onChange={(e) =>
                setSearchParams({ ...searchParams, query: e.target.value })
              }
              className="flex-1"
              size="large"
              allowClear
              onPressEnter={handleSearch}
            />
            <CategoryFilter
              selectedCategory={searchParams.categoryId || null}
              onCategoryChange={(categoryId) =>
                setSearchParams({ ...searchParams, categoryId })
              }
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Space>
            <Checkbox
              checked={searchParams.inStock}
              onChange={(e) =>
                setSearchParams({ ...searchParams, inStock: e.target.checked })
              }
            >
              Còn hàng
            </Checkbox>
            <Checkbox
              checked={searchParams.isActive}
              onChange={(e) =>
                setSearchParams({ ...searchParams, isActive: e.target.checked })
              }
            >
              Đang bán
            </Checkbox>
          </Space>
        </div>

        <div>
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              size="large"
              onClick={handleSearch}
            >
              Tìm kiếm
            </Button>
            <Button
              size="large"
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              Đặt lại
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
}
