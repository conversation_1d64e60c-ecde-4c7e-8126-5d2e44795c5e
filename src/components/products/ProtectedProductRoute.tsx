import { Navigate } from "react-router-dom";
import { useProductPermissions } from "../../hooks/useProductPermissions";
import { message } from "antd";

interface ProtectedProductRouteProps {
  children: React.ReactNode;
}

export const ProtectedProductRoute = ({
  children,
}: ProtectedProductRouteProps) => {
  const { canManageProducts } = useProductPermissions();

  if (!canManageProducts) {
    message.error("Bạn không có quyền truy cập trang này");
    return <Navigate to="/products" replace />;
  }

  return <>{children}</>;
};
