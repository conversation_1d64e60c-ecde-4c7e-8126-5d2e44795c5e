import { useQuery } from "@tanstack/react-query";
import { Select } from "antd";
import { apiCall, endpoints } from "../../lib/api";
import { Category } from "../../types/product";

interface CategoryFilterProps {
  selectedCategory: number | null;
  onCategoryChange: (categoryId: number | null) => void;
}

export function CategoryFilter({
  selectedCategory,
  onCategoryChange,
}: CategoryFilterProps) {
  const { data: categories, isLoading } = useQuery<Category[]>({
    queryKey: ["categories"],
    queryFn: () => apiCall("GET", endpoints.categories.list),
  });

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground min-w-20">Danh mục:</span>
      <Select
        loading={isLoading}
        value={selectedCategory || undefined}
        onChange={(value) => onCategoryChange(value || null)}
        style={{ width: 180 }}
        size="large"
        placeholder="Tất cả danh mục"
        allowClear
        options={categories?.map((category) => ({
          value: category.id,
          label: category.parent
            ? `${category.parent.name} / ${category.name}`
            : category.name,
        }))}
      />
    </div>
  );
}
