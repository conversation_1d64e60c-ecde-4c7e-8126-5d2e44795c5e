import React, { useState } from 'react';
import { Button, Popover, Select, Space, Checkbox } from 'antd'; // Added Checkbox
import { FilterOutlined } from '@ant-design/icons';
import { PAYMENT_STATUS_OPTIONS, Order } from '../../types/order';
// CheckboxValueType is not directly exported, we'll use Order['payment_status'][]
// import { CheckboxOptionType } from 'antd/es/checkbox'; // Not needed if options structure matches


interface OrderFilterControlProps {
  currentPaymentStatus?: Order['payment_status'][]; // Changed to array
  onPaymentStatusChange: (status?: Order['payment_status'][]) => void; // Changed to array
}

const FILTER_TYPES = [
  { label: 'Trạng thái TT', value: 'paymentStatus' },
  // Add other filter types here in the future
];

export const OrderFilterControl: React.FC<OrderFilterControlProps> = ({
  currentPaymentStatus,
  onPaymentStatusChange,
}) => {
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [selectedFilterType, setSelectedFilterType] = useState<string | undefined>(undefined);
  // Temporary state for selected payment status within the popover - now an array
  const [tempPaymentStatus, setTempPaymentStatus] = useState<Order['payment_status'][]>(currentPaymentStatus || []);

  // Sync tempPaymentStatus when popover opens or currentPaymentStatus prop changes
  React.useEffect(() => {
    if (popoverVisible) {
      setTempPaymentStatus(currentPaymentStatus || []);
      if (currentPaymentStatus && currentPaymentStatus.length > 0) {
        setSelectedFilterType('paymentStatus');
      }
    }
  }, [popoverVisible, currentPaymentStatus]);


  const handleFilterTypeChange = (value: string) => {
    setSelectedFilterType(value);
    // Reset temporary specific filter when filter type changes
    if (value !== 'paymentStatus') {
      setTempPaymentStatus([]);
    } else {
      // If switching to paymentStatus, initialize temp with current active filter if any
      setTempPaymentStatus(currentPaymentStatus || []);
    }
  };

  const handleApplyFilters = () => {
    if (selectedFilterType === 'paymentStatus') {
      onPaymentStatusChange(tempPaymentStatus.length > 0 ? tempPaymentStatus : undefined);
    } else {
      onPaymentStatusChange(undefined);
    }
    setPopoverVisible(false);
  };

  const handleResetFilters = () => {
    setSelectedFilterType(undefined);
    setTempPaymentStatus([]); // Clear temporary state
    onPaymentStatusChange(undefined); // Apply the reset
    setPopoverVisible(false); // Close popover after resetting
  };

  const handleTempPaymentStatusChange = (checkedValues: Order['payment_status'][]) => {
    setTempPaymentStatus(checkedValues);
  };

  const content = (
    <Space direction="vertical" style={{ width: 250 }}>
      <Select
        placeholder="Chọn loại lọc"
        style={{ width: '100%' }}
        value={selectedFilterType}
        onChange={handleFilterTypeChange}
        options={FILTER_TYPES}
      />
      {selectedFilterType === 'paymentStatus' && (
        <Checkbox.Group
          style={{ width: '100%' }}
          options={[...PAYMENT_STATUS_OPTIONS]} // Spread to make it mutable and ensure correct type
          value={tempPaymentStatus}
          onChange={handleTempPaymentStatusChange}
        />
      )}
      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
        <Button onClick={handleResetFilters} style={{ flexGrow: 1 }}>
          Reset
        </Button>
        <Button type="primary" onClick={handleApplyFilters} style={{ flexGrow: 1 }}>
          Áp dụng
        </Button>
      </Space>
    </Space>
  );

  const isFilterActive = !!(currentPaymentStatus && currentPaymentStatus.length > 0);

  return (
    <Popover
      content={content}
      title="Bộ lọc đơn hàng"
      trigger="click"
      open={popoverVisible}
      onOpenChange={setPopoverVisible}
      placement="bottomLeft"
    >
      <Button icon={<FilterOutlined />} size="large" type={isFilterActive ? "primary" : "default"}>
        Lọc {isFilterActive && " (Đang lọc)"}
      </Button>
    </Popover>
  );
};