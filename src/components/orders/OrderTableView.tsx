import { Select, Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import {
  Order,
  PAYMENT_METHOD_OPTIONS,
  PAYMENT_STATUS_OPTIONS,
  SHIPPING_UNIT_OPTIONS,
} from "../../types/order";
import {
  Truck,
  CreditCard,
  Banknote,
  ShoppingCart,
  Calendar,
  Bike,
  Car,
  Package,
  User,
  Clock,
} from "lucide-react";
import { formatCurrency } from "../../lib/utils";
import { statusColors } from "./orderUtils";
import { useAuth } from "../../context/auth-hooks";
import { STATUS_OPTIONS } from "../../types/order";
import { Staff } from "../../types/staff";
import { OrderTimer } from "./OrderTimer";
import { isOutsideWorkingHours, isOrderOverdue } from "../../utils/orderTimerUtils";

interface OrderTableViewProps {
  orders: Order[];
  staffList: Staff[];
  onUpdateStatus: (orderId: number, status: Order["status"]) => void;
  onUpdateDeliveryMethod: (
    orderId: number,
    params: { delivery_staff_id?: number | null; shipping_unit?: string }
  ) => void;
  onUpdatePaymentMethod: (
    orderId: number,
    paymentMethod: "cod" | "cash" | "bank_transfer"
  ) => void;
  onUpdatePaymentStatus?: (
    orderId: number,
    paymentStatus: "paid" | "unpaid"
  ) => void;
  onViewDetails: (orderId: number) => void;
}

export function OrderTableView({
  orders,
  staffList,
  onUpdateStatus,
  onUpdateDeliveryMethod,
  onUpdatePaymentMethod,
  onUpdatePaymentStatus,
  onViewDetails,
}: OrderTableViewProps): JSX.Element {
  const { user } = useAuth();
  const isSalesManager = user?.role === "sales_manager";
  const isWarehouseStaff = user?.role === "warehouse_staff";
  const isDeliveryStaff = user?.role === "delivery_staff";

  // Filter delivery staff list
  const deliveryStaffList = staffList.filter(
    (staff) => staff.role === "delivery_staff"
  );

  // Filter shipping unit options (exclude motorbike)
  const filteredShippingUnitOptions = SHIPPING_UNIT_OPTIONS.filter(
    (option) => option.value !== "motorbike"
  );

  // Function to get shipping unit icon
  const getShippingUnitIcon = (shippingUnit: string) => {
    switch (shippingUnit) {
      case "company_vehicle":
        return <Truck className="inline-block mr-2 text-blue-500" size={16} />;
      case "motorbike":
        return <Bike className="inline-block mr-2 text-green-500" size={16} />;
      case "grab":
        return <Car className="inline-block mr-2 text-green-500" size={16} />;
      case "transport_partner":
        return (
          <Truck className="inline-block mr-2 text-orange-500" size={16} />
        );
      case "shipping_partner":
        return (
          <Package className="inline-block mr-2 text-purple-500" size={16} />
        );
      default:
        return null;
    }
  };

  // Create combined options for delivery method dropdown
  const getDeliveryMethodOptions = (record: Order) => {
    const options = [
      { value: "none", label: "Chọn phương thức giao hàng", disabled: true },
      ...deliveryStaffList.map((staff) => ({
        value: `staff_${staff.id}`,
        label: `${staff.first_name} ${staff.last_name} (Nhân viên)`,
        icon: <User className="inline-block mr-2 text-blue-500" size={16} />,
      })),
      ...filteredShippingUnitOptions.map((option) => ({
        value: `unit_${option.value}`,
        label: option.label,
        icon: getShippingUnitIcon(option.value),
      })),
    ];

    return options;
  };

  // Get current delivery method value
  const getDeliveryMethodValue = (record: Order) => {
    if (record.delivery_staff) {
      return `staff_${record.delivery_staff.id}`;
    }

    if (record.shipping_unit && record.shipping_unit !== "motorbike") {
      return `unit_${record.shipping_unit}`;
    }

    return "none";
  };

  // Handle delivery method change
  const handleDeliveryMethodChange = (value: string, orderId: number) => {
    if (value === "none") return;

    const [type, ...idParts] = value.split("_");
    // Join the parts back together to handle shipping unit values with underscores
    const id = idParts.join("_");

    if (type === "staff") {
      onUpdateDeliveryMethod(orderId, {
        delivery_staff_id: parseInt(id),
        shipping_unit: "motorbike",
      });
    } else if (type === "unit") {
      // Make sure we're sending the full shipping unit value
      // Explicitly set delivery_staff_id to null when selecting shipping units
      onUpdateDeliveryMethod(orderId, {
        shipping_unit: id,
        delivery_staff_id: null,
      });
    }
  };

  // Handle payment method change
  const handlePaymentMethodChange = (
    value: "cod" | "cash" | "bank_transfer",
    orderId: number
  ) => {
    onUpdatePaymentMethod(orderId, value);
  };

  // Handle payment status change
  const handlePaymentStatusChange = (
    value: "paid" | "unpaid",
    orderId: number
  ) => {
    if (onUpdatePaymentStatus) {
      onUpdatePaymentStatus(orderId, value);
    }
  };

  const columns: ColumnsType<Order> = [
    {
      title: "Mã ĐH",
      dataIndex: "id",
      key: "id",
      render: (id: number) => (
        <div className="flex items-center">
          <ShoppingCart className="inline-block mr-2 text-blue-500" size={16} />
          <span>{id}</span>
        </div>
      ),
      width: 100,
    },
    {
      title: "Ngày tạo",
      dataIndex: "created_at",
      key: "created_at",
      render: (date: string) => {
        const formatDate = (dateStr: string) => {
          const d = new Date(dateStr);
          return `${d.getDate().toString().padStart(2, "0")}/${(
            d.getMonth() + 1
          )
            .toString()
            .padStart(2, "0")}/${d.getFullYear().toString().slice(-2)}`;
        };

        return (
          <div className="flex items-center">
            <Calendar className="inline-block mr-2 text-blue-500" size={16} />
            <span>{formatDate(date)}</span>
          </div>
        );
      },
      width: 120,
    },
    {
      title: "Thời gian xử lý",
      key: "timer",
      render: (_, record) => {
        // Chỉ hiển thị timer cho các đơn hàng đang xử lý hoặc đang giao
        if (record.status === "processing" || record.status === "shipped") {
          const isPaused = isOutsideWorkingHours();
          const isOverdue = isOrderOverdue(record.confirmation_time);

          let clockColor = "text-green-500"; 
          if (isOverdue) {
            clockColor = "text-red-500"; 
          } else if (isPaused) {
            clockColor = "text-gray-500";
          }

          return (
            <div className="flex items-center">
              <Clock
                className={`inline-block mr-2 ${clockColor}`}
                size={16}
              />
              <OrderTimer order={record} />
            </div>
          );
        }
        // Hiển thị thời gian hoàn thành cho đơn hàng đã hoàn thành
        else if (record.status === "delivered" && record.completion_time) {
          return (
            <div className="flex items-center">
              <Clock className="inline-block mr-2 text-black" size={16} />
              <span>Hoàn thành lúc: <OrderTimer order={record} /></span>
            </div>
          );
        }
        return "—";
      },
      width: 150,
    },
    {
      title: "Khách hàng",
      key: "customer",
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.user.full_name}</div>
          <div className="text-sm text-muted-foreground">
            {record.phone_number}
          </div>
        </div>
      ),
      width: 200,
    },
    {
      title: "Địa chỉ",
      key: "address",
      render: (_, record) => (
        <div className="text-sm">
          <div className="line-clamp-2">
            {record.shipping_address}, Phường {record.ward}, Quận{" "}
            {record.district}, Thành phố {record.city}
          </div>
        </div>
      ),
      width: 250,
    },
    {
      title: "Sản phẩm",
      key: "products",
      render: (_, record) => (
        <div>
          {record.items.map((item, i) => (
            <div key={i} className="text-sm">
              {item.product_name}{" "}
              {item.variant_name && `(${item.variant_name})`} x{item.quantity}
            </div>
          ))}
        </div>
      ),
      width: 200,
    },
    {
      title: "Phương thức TT",
      key: "payment_method",
      render: (_, record) => {
        const getIcon = (method: string) => {
          switch (method) {
            case "cod":
              return (
                <Truck
                  className="inline-block mr-2 text-orange-500"
                  size={18}
                />
              );
            case "bank_transfer":
              return (
                <CreditCard
                  className="inline-block mr-2 text-blue-500"
                  size={18}
                />
              );
            case "cash":
              return (
                <Banknote
                  className="inline-block mr-2 text-green-500"
                  size={18}
                />
              );
            default:
              return null;
          }
        };

        // Create options for payment method dropdown
        const options = PAYMENT_METHOD_OPTIONS.map((option) => ({
          value: option.value,
          label: (
            <div className="flex items-center">
              {getIcon(option.value)}
              <span>{option.label}</span>
            </div>
          ),
        }));

        // If not a sales manager, just display the current value
        if (!isSalesManager) {
          const method = PAYMENT_METHOD_OPTIONS.find(
            (opt) => opt.value === record.payment_method
          );

          if (!method) return "—";

          return (
            <div className="flex items-center">
              {getIcon(record.payment_method || "")}
              <span>{method.label}</span>
            </div>
          );
        }

        // Return select component for managers
        return (
          <Select
            value={record.payment_method || undefined}
            onChange={(value) => handlePaymentMethodChange(value, record.id)}
            style={{ width: 160 }}
            options={options}
            placeholder="Chọn phương thức"
          />
        );
      },
      width: 175,
    },
    {
      title: "Trạng thái TT",
      key: "payment_status",
      // filters: PAYMENT_STATUS_OPTIONS.map(opt => ({ text: opt.label, value: opt.value })),
      // onFilter: (value, record) => record.payment_status === value,
      render: (_, record) => {
        // Create options for payment status dropdown with colored text
        const options = PAYMENT_STATUS_OPTIONS.map((option) => ({
          value: option.value,
          label: (
            <div className="flex items-center">
              <span
                className={
                  option.value === "paid" ? "text-green-500" : "text-orange-500"
                }
              >
                {option.label}
              </span>
            </div>
          ),
        }));

        // If not a sales manager, just display the current value
        if (!isSalesManager) {
          const status = PAYMENT_STATUS_OPTIONS.find(
            (opt) => opt.value === record.payment_status
          );

          if (!status) return "—";

          return (
            <div
              className={`flex items-center ${
                record.payment_status === "paid"
                  ? "text-green-500"
                  : "text-orange-500"
              }`}
            >
              <span>{status.label}</span>
            </div>
          );
        }

        // Return select component for managers with colored text
        return (
          <Select
            value={record.payment_status}
            onChange={(value) => handlePaymentStatusChange(value, record.id)}
            style={{ width: 160 }}
            options={options}
            placeholder="Chọn trạng thái"
            className={
              record.payment_status === "paid"
                ? "text-green-500"
                : "text-orange-500"
            }
          />
        );
      },
      width: 175,
    },
    {
      title: "Tổng tiền",
      key: "total",
      render: (_, record) => formatCurrency(record.final_total),
      width: 150,
    },
    {
      title: "Trạng thái",
      key: "status",

      render: (_, record) => {
        if (isSalesManager) {
          return (
            <Select
              value={record.status}
              onChange={(value) => onUpdateStatus(record.id, value)}
              style={{ width: 150 }}
              options={STATUS_OPTIONS.map((option) => ({
                value: option.value,
                label: (
                  <span
                    className={`px-2 py-1 inline-block rounded w-full ${
                      statusColors[option.value as Order["status"]]
                    }`}
                  >
                    {option.label}
                  </span>
                ),
              }))}
            />
          );
        }

        const statusText = (() => {
          switch (record.status) {
            case "pending":
              return "Chờ xác nhận";
            case "processing":
              return "Đang xử lý";
            case "shipped":
              return "Đã gửi hàng";
            case "delivered":
              return "Đã giao hàng";
            case "cancelled":
              return "Đã hủy";
            case "returned":
              return "Đã trả hàng";
            default:
              return "—";
          }
        })();

        return (
          <div className="space-y-2">
            <div
              className={`rounded px-2 py-1 text-sm ${
                statusColors[record.status]
              } `}
            >
              {statusText}
            </div>
            {isWarehouseStaff && record.status === "processing" && (
              <button
                onClick={() => onUpdateStatus(record.id, "shipped")}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded px-2 py-1 text-sm"
              >
                Đã xuất kho
              </button>
            )}
            {isDeliveryStaff && record.status === "shipped" && (
              <button
                onClick={() => onUpdateStatus(record.id, "delivered")}
                className="w-full bg-green-500 hover:bg-green-600 text-white rounded px-2 py-1 text-sm"
              >
                Đã giao
              </button>
            )}
          </div>
        );
      },
      width: 175,
    },
    {
      title: "NVBH",
      key: "sales_admin",
      render: (_, record) =>
        record.sales_admin
          ? `${record.sales_admin.first_name} ${record.sales_admin.last_name}`
          : "Khách hàng tự đặt",
      width: 150,
    },
    {
      title: "Phương thức giao hàng",
      key: "delivery_method",
      render: (_, record) => {
        // Current value for the select
        const currentValue = getDeliveryMethodValue(record);

        // Create options array
        const options = [
          {
            value: "none",
            label: "Chọn phương thức giao hàng",
            disabled: true,
          },
          ...deliveryStaffList.map((staff) => ({
            value: `staff_${staff.id}`,
            label: (
              <div className="flex items-center">
                <User className="inline-block mr-2 text-blue-500" size={16} />
                <span>
                  {staff.first_name} {staff.last_name} (Nhân viên)
                </span>
              </div>
            ),
          })),
          ...filteredShippingUnitOptions.map((option) => ({
            value: `unit_${option.value}`,
            label: (
              <div className="flex items-center">
                {getShippingUnitIcon(option.value)}
                <span>{option.label}</span>
              </div>
            ),
          })),
        ];

        // Display current value if not using select
        if (!isSalesManager) {
          if (record.delivery_staff) {
            return (
              <div className="flex items-center">
                <User className="inline-block mr-2 text-blue-500" size={16} />
                <span>
                  {record.delivery_staff.first_name}{" "}
                  {record.delivery_staff.last_name} (Nhân viên)
                </span>
              </div>
            );
          }

          if (record.shipping_unit) {
            const unit = SHIPPING_UNIT_OPTIONS.find(
              (opt) => opt.value === record.shipping_unit
            );

            if (unit) {
              return (
                <div className="flex items-center">
                  {getShippingUnitIcon(record.shipping_unit)}
                  <span>{unit.label}</span>
                </div>
              );
            }
          }

          return "—";
        }

        // Return select component for managers
        return (
          <Select
            value={currentValue}
            onChange={(value) => handleDeliveryMethodChange(value, record.id)}
            style={{ width: 200 }}
            options={options}
          />
        );
      },
      width: 200,
    },
    {
      title: "Ghi chú",
      dataIndex: "notes",
      key: "notes",
      render: (notes: string) => notes || "—",
      width: 200,
    },
    {
      title: "",
      key: "actions",
      render: (_, record) => (
        <button
          onClick={() => onViewDetails(record.id)}
          className="text-primary hover:text-primary/90 text-sm font-medium"
        >
          Xem chi tiết
        </button>
      ),
      width: 100,
      fixed: "right",
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={orders}
      rowKey="id"
      scroll={{ x: 2450 }}
      pagination={false}
      rowClassName={(_, index) => (index % 2 === 0 ? "bg-white" : "bg-gray-50")}
    />
  );
}
