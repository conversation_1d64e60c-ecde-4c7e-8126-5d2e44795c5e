import { Card, Typography } from "antd";

const { Title } = Typography;

interface CreateOrderNotesProps {
  notes: string;
  onChange: (value: string) => void;
}

export default function CreateOrderNotes({
  notes,
  onChange,
}: CreateOrderNotesProps) {
  return (
    <Card>
      <Title level={4}>Ghi chú </Title>
      <textarea
        value={notes}
        onChange={(e) => onChange(e.target.value)}
        rows={3}
        className="w-full border rounded p-2"
        placeholder="Ghi chú đơn hàng"
      />
    </Card>
  );
}
