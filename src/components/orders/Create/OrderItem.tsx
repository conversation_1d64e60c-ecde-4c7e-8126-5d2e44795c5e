import { Card, Button, Input, Checkbox } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

interface OrderItemProps {
  item: {
    product_name: string;
    variant_name?: string;
    price: number;
    quantity: number;
    total_price: number;
    unit?: string;
  };
  index: number;
  isShowroom?: boolean;
  onRemove: (index: number) => void;
  onQuantityChange: (index: number, value: number) => void;
  onTotalPriceChange: (index: number, value: number) => void;
  onUnitChange: (index: number, value: string) => void;
}

export function OrderItem({
  item,
  index,
  isShowroom = false,
  onRemove,
  onQuantityChange,
  onTotalPriceChange,
  onUnitChange,
}: OrderItemProps) {
  return (
    <Card className="mb-4">
      <div className="flex justify-between items-start mb-4">
        <div>
          <div className="font-medium">{item.product_name}</div>
          {item.variant_name && (
            <div className="text-sm text-gray-500">
              <PERSON><PERSON><PERSON>n thể: {item.variant_name}
            </div>
          )}
          <div className="text-sm">
            Giá: {isShowroom ? "0" : Math.round(item.price).toLocaleString()} VND
          </div>
        </div>
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => onRemove(index)}
        >
          Xóa
        </Button>
      </div>

      <div className="grid grid-rows-2 gap-4 items-end">
        <div className="flex flex-row justify-between items-center gap-4 ">
          <div className="text-sm font-medium whitespace-nowrap">Số lượng</div>
          <Input
            type="number"
            min={1}
            value={item.quantity}
            onChange={(e) =>
              onQuantityChange(index, parseInt(e.target.value) || 1)
            }
            className="w-32"
          />
          {/* <div className="flex-grow">
            <Checkbox
              checked={item.unit === "box"}
              onChange={(e) =>
                onUnitChange(index, e.target.checked ? "box" : "piece")
              }
              className="h-full flex items-center"
            >
              <span className="text-base">Tính theo thùng</span>
            </Checkbox>
          </div> */}
        </div>

        <div>
          <div className="text-sm font-medium mb-1">Tổng tiền</div>
          <div className="flex gap-2 items-center">
            <Input
              type="text"
              min={0}
              value={isShowroom ? "0" : item.total_price.toLocaleString()}
              disabled={isShowroom}
              onChange={(e) => {
                const value = e.target.value.replace(/[^\d]/g, "");
                onTotalPriceChange(index, parseInt(value) || 0);
              }}
              className="w-full"
              addonAfter="VND"
            />
          </div>
        </div>
      </div>
    </Card>
  );
}
