import React from "react";
import { Select, Checkbox, Typography, Card } from "antd";

const { Title } = Typography;

interface CreateOrderPaymentMethodProps {
  paymentMethod: "cod" | "cash" | "bank_transfer";
  paymentStatus: "paid" | "unpaid";
  companyPaymentReceived: boolean;
  onPaymentMethodChange: (value: "cod" | "cash" | "bank_transfer") => void;
  onPaymentStatusChange: (value: "paid" | "unpaid") => void;
  onCompanyPaymentReceivedChange: (value: boolean) => void;
}

export default function CreateOrderPaymentMethod({
  paymentMethod,
  paymentStatus,
  companyPaymentReceived,
  onPaymentMethodChange,
  onPaymentStatusChange,
  onCompanyPaymentReceivedChange,
}: CreateOrderPaymentMethodProps) {
  return (
    <Card style={{ marginTop: "32px" }}>
      <Title level={4}>Thông tin thanh toán</Title>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <div>
          <Typography.Text
            strong
            style={{ display: "block", marginBottom: "8px" }}
          >
            Phương thức thanh toán
          </Typography.Text>
          <Select
            value={paymentMethod}
            onChange={onPaymentMethodChange}
            style={{ width: "100%" }}
            options={[
              { value: "cod", label: "COD" },
              { value: "cash", label: "Tiền mặt" },
              { value: "bank_transfer", label: "Chuyển khoản" },
            ]}
          />
        </div>
        <div>
          <Typography.Text
            strong
            style={{ display: "block", marginBottom: "8px" }}
          >
            Trạng thái thanh toán
          </Typography.Text>
          <Select
            value={paymentStatus}
            onChange={onPaymentStatusChange}
            style={{ width: "100%" }}
            options={[
              { value: "paid", label: "Đã thanh toán" },
              { value: "unpaid", label: "Chưa thanh toán" },
            ]}
          />
        </div>
      </div>

      <div style={{ marginTop: "16px" }}>
        <Checkbox
          checked={companyPaymentReceived}
          onChange={(e) => onCompanyPaymentReceivedChange(e.target.checked)}
        >
          Công ty đã nhận được thanh toán
        </Checkbox>
      </div>
    </Card>
  );
}
