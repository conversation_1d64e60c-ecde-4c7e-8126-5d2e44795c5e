import React from "react";
import {
  Button,
  Empty,
  Card,
  Typography,
  Divider,
  Space,
  Statistic,
} from "antd";
import { PlusOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import { OrderItem as OrderItemType } from "../../../types/order";

// Extend OrderItem type to include unit property
interface ExtendedOrderItem extends OrderItemType {
  unit?: string;
}

interface ProductSectionProps {
  items: ExtendedOrderItem[];
  totalPrice: number;
  isShowroom?: boolean;
  onAddProduct: () => void;
  onRemoveItem: (index: number) => void;
  onQuantityChange: (index: number, quantity: number) => void;
  onTotalPriceChange: (index: number, totalPrice: number) => void;
  onUnitChange: (index: number, unit: string) => void;
}

const { Title } = Typography;

const ProductSection: React.FC<ProductSectionProps> = ({
  items,
  totalPrice,
  isShowroom = false,
  onAddProduct,
  onRemoveItem,
  onQuantityChange,
  onTotalPriceChange,
  onUnitChange,
}) => {
  return (
    <div>
      <Card className="mb-4">
        <div className="flex justify-between items-center mb-4">
          <span className="font-bold text-xl">Sản phẩm</span>
          <Button type="primary" icon={<PlusOutlined />} onClick={onAddProduct}>
            Thêm sản phẩm
          </Button>
        </div>
        {items.length === 0 ? (
          <Empty
            description="Chưa có sản phẩm nào"
            className="border-2 border-dashed rounded py-8 my-4"
          />
        ) : (
          <div className="space-y-4">
            {items.map((item, index) => (
              <OrderItem
                key={index}
                item={item}
                index={index}
                isShowroom={isShowroom}
                onRemove={onRemoveItem}
                onQuantityChange={onQuantityChange}
                onTotalPriceChange={onTotalPriceChange}
                onUnitChange={onUnitChange}
              />
            ))}

            <Divider />
            <div className="flex justify-end">
              <Statistic
                title="Tổng cộng"
                value={Math.round(totalPrice)}
                suffix="VND"
                groupSeparator=","
                className="text-right"
              />
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

// Import the existing OrderItem component
import { OrderItem } from "./OrderItem";

export default ProductSection;
