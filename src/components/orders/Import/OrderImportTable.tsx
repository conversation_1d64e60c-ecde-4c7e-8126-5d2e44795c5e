// src/components/OrderImport/OrderImportTable.tsx
import React, { useMemo } from "react";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import { Check, X, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  formatDate,
  formatCurrencyWith3Zeros,
  formatCurrency,
} from "@/utils/importHelper";
import { ITEMS_PER_PAGE } from "@/utils/constants";

interface ImportedRow {
  [key: string]: string | number; // Allow both string and number values
}

interface RowStatus {
  success?: boolean;
  message: string;
  loading?: boolean;
}

interface OrderImportTableProps {
  data: ImportedRow[];
  processedRows: { [key: number]: RowStatus };
  hasErrors?: boolean;
  onDownloadErrors?: () => void;
}

// Thêm key hoặc id duy nhất cho mỗi dòng dữ liệu để Ant Table hoạt động tốt nhất
const addKeyToData = (data: ImportedRow[]): (ImportedRow & { key: number })[] =>
  data.map((row, index) => ({ ...row, key: index })); // Dùng index làm key tạm thời

export const OrderImportTable: React.FC<OrderImportTableProps> = ({
  data,
  processedRows,
  hasErrors,
  onDownloadErrors,
}) => {
  // Thêm key vào data
  const dataSourceWithKeys = useMemo(() => addKeyToData(data), [data]);

  const columns: ColumnsType<ImportedRow & { key: number }> = useMemo(
    () => [
      // Thêm key vào kiểu dữ liệu
      {
        title: "Trạng thái cập nhật",
        dataIndex: "key", // Dùng key để lấy trạng thái từ processedRows
        key: "status", // Thêm key cho cột
        width: 300,
        render: (key: number) => (
          <div className="flex items-center gap-2">
            {processedRows[key] && (
              <>
                {processedRows[key].loading ? (
                  <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                ) : processedRows[key].success ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <X className="w-4 h-4 text-red-500" />
                )}
                <span
                  className={
                    processedRows[key].success
                      ? "text-green-600"
                      : "text-red-500"
                  }
                >
                  {processedRows[key].message}
                </span>
              </>
            )}
          </div>
        ),
        fixed: "left", // Ghim cột trạng thái
      },
      { title: "Họ và Tên", dataIndex: "Họ và Tên", width: 200, fixed: "left" }, // Ghim cột Họ và Tên
      { title: "Số Điện Thoại", dataIndex: "Số Điện Thoại", width: 120 },
      { title: "Đường", dataIndex: "Đường", width: 200 },
      { title: "Phường", dataIndex: "Phường", width: 100 },
      { title: "Quận", dataIndex: "Quận", width: 100 },
      { title: "Tỉnh/Thành phố", dataIndex: "Tỉnh/Thành phố", width: 120 },
      {
        title: "Ngày Order",
        dataIndex: "Ngày Order",
        width: 120,
        render: formatDate,
      },
      { title: "NVBH", dataIndex: "NVBH", width: 100 },
      { title: "Sản Phẩm", dataIndex: "Sản Phẩm", width: 200 },
      {
        title: "Giá Tiền",
        dataIndex: "Giá Tiền",
        width: 120,
        render: formatCurrencyWith3Zeros,
      },
      {
        title: "Phí VC",
        dataIndex: "Phí VC",
        width: 100,
        render: formatCurrencyWith3Zeros,
      },
      {
        title: "Doanh số",
        dataIndex: "Doanh số",
        width: 120,
        render: formatCurrency,
      },
      {
        title: "Phương Thức TT",
        dataIndex: "Phương Thức Thanh Toán",
        width: 150,
      },
      {
        title: "Ngày Giao",
        dataIndex: "Ngày Giao",
        width: 120,
        render: formatDate,
      },
      { title: "Đơn Vị VC", dataIndex: "Đơn Vị Vận Chuyển", width: 150 },
      { title: "Trạng Thái ĐH", dataIndex: "Trạng Thái Đơn Hàng", width: 150 },
      { title: "NV Giao Hàng", dataIndex: "Nhân viên giao hàng", width: 150 },
      { title: "Note", dataIndex: "Note", width: 200 },
    ],
    [processedRows]
  ); // Cập nhật columns khi processedRows thay đổi

  if (data.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {hasErrors && onDownloadErrors && (
        <div className="flex justify-end">
          <Button onClick={onDownloadErrors} variant="outline" className="mb-2">
            Tải xuống đơn hàng lỗi
          </Button>
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        <Table<ImportedRow & { key: number }> // Chỉ định kiểu dữ liệu rõ ràng
          dataSource={dataSourceWithKeys}
          columns={columns}
          pagination={{
            total: data.length,
            pageSize: ITEMS_PER_PAGE,
            showSizeChanger: false,
            position: ["bottomCenter"],
          }}
          scroll={{ x: 2100, y: "calc(100vh - 400px)" }} // Điều chỉnh lại scroll x và y nếu cần
          rowKey="key" // Chỉ định key cho mỗi hàng
        />
      </div>
    </div>
  );
};
