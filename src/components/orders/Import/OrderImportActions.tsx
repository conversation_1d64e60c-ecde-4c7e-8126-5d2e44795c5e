// src/components/OrderImport/OrderImportActions.tsx
import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface OrderImportActionsProps {
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearData: () => void;
  onSaveData: () => void;
  isSaving: boolean;
  hasData: boolean;
}

export const OrderImportActions: React.FC<OrderImportActionsProps> = ({
  onFileChange,
  onClearData,
  onSaveData,
  isSaving,
  hasData,
}) => (
  <div className="flex items-center gap-4 mb-6">
    <Button asChild className="cursor-pointer">
      <label>
        Chọn File (CSV/Excel)
        <input
          type="file"
          className="hidden"
          accept=".csv,.xlsx,.xls"
          onChange={onFileChange}
        />
      </label>
    </Button>
    <Button onClick={onClearData} variant="outline" disabled={!hasData}>
      Xoá dữ liệu
    </Button>
    <Button
      onClick={onSaveData}
      disabled={!hasData || isSaving}
      variant="secondary"
    >
      {isSaving ? "Đang lưu..." : "Lưu thông tin"}
    </Button>
  </div>
);
