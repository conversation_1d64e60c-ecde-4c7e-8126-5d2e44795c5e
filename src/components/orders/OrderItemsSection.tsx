import { useState, useEffect, useMemo, useRef } from "react";
import { Order, OrderItem } from "../../types/order";
import { formatCurrency } from "../../lib/utils";
import { Button, InputNumber, Table } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";

interface OrderItemsSectionProps {
  order: Order;
  onUpdateOrder: (data: Partial<Order>) => void;
  onAddProduct?: () => void;
  editingItem: number | null;
  onEditItem?: (id: number | null) => void;
  disabled?: boolean;
  isMobile?: boolean;
}

type EditingField = "quantity" | "total_price" | null;

export function OrderItemsSection({
  order,
  onUpdateOrder,
  onAddProduct,
  editingItem,
  onEditItem,
  disabled = false,
  isMobile = false,
}: OrderItemsSectionProps) {
  const [editingField, setEditingField] = useState<EditingField>(null);

  // Local state for order items and other editable fields
  const [orderItems, setOrderItems] = useState<OrderItem[]>(order.items);
  const [shippingFee, setShippingFee] = useState(order.shipping_fee || 0);
  const [discount, setDiscount] = useState(order.discount || 0);
  const [tax, setTax] = useState(order.tax || 0);

  // Create a ref at the top level to track if this is the initial load
  const isInitialLoad = useRef(true);

  // Update local state when order changes, but only on initial load or when items change
  useEffect(() => {
    if (isInitialLoad.current) {
      // On initial load, set all values from the order
      setOrderItems(order.items);
      setShippingFee(order.shipping_fee || 0);
      setDiscount(order.discount || 0);
      setTax(order.tax || 0);
      isInitialLoad.current = false;
    } else {
      // On subsequent updates, we need to properly merge items
      // This should handle both new items and updated quantities for existing items

      // We need to completely replace the items with the new ones from the order
      // This ensures we get all updates, including quantity changes for existing items
      setOrderItems(order.items);

      // Always update these values
      setShippingFee(order.shipping_fee || 0);
      setDiscount(order.discount || 0);
      setTax(order.tax || 0);
    }
  }, [order]);

  // Calculate subtotal based on current items
  const subtotal = useMemo(() => {
    return orderItems.reduce(
      (sum, item) => sum + Number(item.total_price || 0),
      0
    );
  }, [orderItems]);

  // Calculate final total
  const finalTotal = useMemo(() => {
    const subTotal = Math.round(Number(subtotal) || 0);
    const shipping = Math.round(Number(shippingFee) || 0);
    const disc = Math.round(Number(discount) || 0);
    const taxRate = Number(tax) || 0;

    const totalBeforeTax = subTotal + shipping - disc;
    return totalBeforeTax * (1 + taxRate);
  }, [subtotal, shippingFee, discount, tax]);

  // Update parent component with all changes
  useEffect(() => {
    if (disabled) return;

    // Create a new order object with all the changes
    const updatedOrder: Partial<Order> = {
      items: orderItems,
      shipping_fee: shippingFee,
      discount: discount,
      tax: tax,
      total_price: Math.round(finalTotal),
    };

    // Send all changes to parent component
    onUpdateOrder(updatedOrder);
  }, [
    orderItems,
    shippingFee,
    discount,
    tax,
    finalTotal,
    onUpdateOrder,
    disabled,
  ]);

  // Handle quantity change for an item
  const handleQuantityChange = (item: OrderItem, newQuantity: number) => {
    if (newQuantity <= 0 || disabled) return;

    setOrderItems((prevItems) =>
      prevItems.map((prevItem) => {
        if (prevItem.id === item.id) {
          // Update the quantity and recalculate total price
          const newTotalPrice = newQuantity * prevItem.price;
          return {
            ...prevItem,
            quantity: newQuantity,
            total_price: newTotalPrice,
          };
        }
        return prevItem;
      })
    );
  };

  // Handle total price change for an item
  const handleTotalPriceChange = (item: OrderItem, newTotalPrice: number) => {
    if (newTotalPrice < 0 || disabled) return;

    setOrderItems((prevItems) =>
      prevItems.map((prevItem) => {
        if (prevItem.id === item.id) {
          // Update the total price and recalculate unit price
          const newUnitPrice = newTotalPrice / prevItem.quantity;
          return {
            ...prevItem,
            total_price: newTotalPrice,
            price: Math.round(newUnitPrice),
          };
        }
        return prevItem;
      })
    );
  };

  // Handle removing an item
  const handleRemoveItem = (itemId: number) => {
    if (disabled) return;

    // Filter out the removed item
    // We need to be careful with items that have id=0 (newly added items from the server)
    // We'll use a more precise comparison to ensure we only remove the exact item
    const updatedItems = orderItems.filter((item, index, array) => {
      // If this is not the item with the ID we're looking for, keep it
      if (item.id !== itemId) return true;

      // If we're here, this item has the ID we're looking for
      // If this is a regular item (positive ID), we can safely remove it
      if (itemId > 0) return false;

      // For items with ID <= 0 (temporary IDs), we need to be more careful
      // Find the index of this item in the array
      const itemIndex = array.findIndex((i) => i.id === itemId);

      // Only remove if this is the exact item at that index
      return index !== itemIndex;
    });

    // Update local state
    setOrderItems(updatedItems);

    // Calculate new subtotal
    const newSubtotal = updatedItems.reduce(
      (sum, item) => sum + (item.total_price || 0),
      0
    );

    // Immediately update parent component to ensure consistency
    onUpdateOrder({
      items: updatedItems,
      total_price: Math.round(
        (newSubtotal + shippingFee - discount) * (1 + tax)
      ),
    });
  };

  return (
    <div className="mb-6 bg-white rounded-lg border overflow-hidden">
      <div className="p-6 pb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold">Chi tiết đơn hàng</h3>
        {onAddProduct && (
          <Button type="primary" onClick={onAddProduct} icon={<PlusOutlined />}>
            {!isMobile && "Thêm sản phẩm"}
          </Button>
        )}
      </div>
      <div className={isMobile ? "overflow-x-auto" : ""}>
        <Table
          scroll={isMobile ? { x: 800 } : undefined}
          dataSource={orderItems}
          pagination={false}
          rowKey="id"
          columns={[
            {
              title: "Sản phẩm",
              dataIndex: "product_name",
              key: "product_name",
              width: isMobile ? 200 : 400,
              render: (text, record: OrderItem) => (
                <div>
                  <div className="font-medium">{text}</div>
                  {record.variant_name && (
                    <div className="text-sm text-gray-500">
                      {record.variant_name}
                    </div>
                  )}
                </div>
              ),
            },
            {
              title: "Số lượng",
              dataIndex: "quantity",
              key: "quantity",
              render: (_, record: OrderItem) =>
                editingItem === record.id ? (
                  <InputNumber
                    min={1}
                    value={record.quantity}
                    onChange={(value) =>
                      handleQuantityChange(record, Number(value))
                    }
                    onBlur={() => {
                      setEditingField(null);
                      onEditItem?.(null);
                    }}
                    autoFocus={editingField === "quantity"}
                    disabled={disabled}
                    className="w-20 !text-right"
                  />
                ) : (
                  <div
                    onClick={() => {
                      setEditingField("quantity");
                      onEditItem?.(record.id);
                    }}
                    className={`${
                      onEditItem && !disabled
                        ? "cursor-pointer hover:text-blue-600"
                        : ""
                    }`}
                  >
                    {record.quantity}
                  </div>
                ),
            },
            {
              title: "Đơn giá",
              dataIndex: "price",
              key: "price",
              render: (price) => formatCurrency(price),
            },
            {
              title: "Thành tiền",
              dataIndex: "total_price",
              key: "total_price",
              render: (_, record: OrderItem) => (
                <div className="flex justify-between items-center">
                  {editingItem === record.id ? (
                    <InputNumber
                      min={0}
                      value={Math.round(record.total_price)}
                      onChange={(value) =>
                        handleTotalPriceChange(record, Number(value))
                      }
                      onBlur={() => {
                        setEditingField(null);
                        onEditItem?.(null);
                      }}
                      autoFocus={editingField === "total_price"}
                      disabled={disabled}
                      className="w-32 !text-right"
                    />
                  ) : (
                    <div
                      onClick={() => {
                        setEditingField("total_price");
                        onEditItem?.(record.id);
                      }}
                      className={`${
                        onEditItem && !disabled
                          ? "cursor-pointer hover:text-blue-600"
                          : ""
                      }`}
                    >
                      {formatCurrency(record.total_price)}
                    </div>
                  )}
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveItem(record.id)}
                    disabled={disabled}
                  />
                </div>
              ),
            },
          ]}
          summary={() => (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 3}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Tạm tính:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 3 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"} `}
                >
                  {formatCurrency(subtotal)}
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 3}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Phí giao hàng:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 3 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  <InputNumber
                    min={0}
                    value={Math.round(shippingFee)}
                    onChange={(value) => {
                      if (!disabled) {
                        setShippingFee(Math.round(Number(value)) || 0);
                      }
                    }}
                    className="w-32 !text-right"
                    disabled={disabled}
                  />
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 3}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Giảm giá:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 3 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  <InputNumber
                    min={0}
                    value={Math.round(discount)}
                    onChange={(value) => {
                      if (!disabled) {
                        setDiscount(Math.round(Number(value)) || 0);
                      }
                    }}
                    className="w-32 !text-right"
                    disabled={disabled}
                  />
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 3}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Thuế:
                </Table.Summary.Cell>
                <Table.Summary.Cell colSpan={isMobile ? 3 : 1} index={1}>
                  <div
                    className={`flex items-center gap-2 ${
                      isMobile ? "justify-start" : "justify-end"
                    }`}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      value={tax * 100}
                      onChange={(value) => {
                        if (!disabled) {
                          setTax((Number(value) || 0) / 100);
                        }
                      }}
                      className="w-30 !text-right"
                      disabled={disabled}
                    />
                    <span>%</span>
                  </div>
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row className="bg-gray-50 font-medium">
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 3}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Tổng cộng:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 3 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  {formatCurrency(finalTotal)}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </>
          )}
        />
      </div>
    </div>
  );
}
