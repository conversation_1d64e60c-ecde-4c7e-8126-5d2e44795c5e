import { useState, useEffect, useCallback } from "react";
import { Product, ProductListItem } from "../../types/product";
import { OrderItem } from "../../types/order";
import { api, endpoints } from "../../lib/api";
import { ProductSearch, ProductSearchParams } from "../products/ProductSearch";
import {
  Modal,
  Row,
  Col,
  Card,
  List,
  Button,
  Select,
  InputNumber,
  Typography,
  Spin,
  Empty,
  Divider,
  Avatar,
} from "antd";
import { DeleteOutlined, ShoppingCartOutlined } from "@ant-design/icons";

const { Text } = Typography;
const { Option } = Select;

interface SelectedProductState {
  id: string; // Unique identifier for each selection
  product: Product;
  variant: number | undefined;
  quantity: number;
  customTotalPrice?: number;
  unit: string;
}

interface ProductSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (items: OrderItem[]) => void;
}

export default function ProductSelectionDialog({
  isOpen,
  onClose,
  onAdd,
}: ProductSelectionDialogProps) {
  const [products, setProducts] = useState<ProductListItem[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<
    SelectedProductState[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<ProductSearchParams>({});

  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get(endpoints.products.list, {
        params: {
          search: searchParams.query,
          category: searchParams.categoryId,
          in_stock: searchParams.inStock,
          is_active: searchParams.isActive,
        },
      });
      setProducts(response.data.results);
    } catch (error) {
      console.error("Không thể tải danh sách sản phẩm:", error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    if (isOpen) {
      loadProducts();
    }
  }, [isOpen, loadProducts]);

  const handleRemoveProduct = (selectionId: string) => {
    setSelectedProducts((prev) => prev.filter((p) => p.id !== selectionId));
  };

  const handleProductClick = async (product: ProductListItem) => {
    try {
      const response = await api.get(endpoints.products.detail(product.id));
      const newProduct: SelectedProductState = {
        id: `${response.data.id}-${Date.now()}`, // Unique ID combining product ID and timestamp
        product: response.data,
        variant: undefined,
        quantity: 1,
        unit: response.data.unit,
      };
      setSelectedProducts([...selectedProducts, newProduct]);
    } catch (error) {
      console.error("Không thể tải thông tin chi tiết sản phẩm:", error);
    }
  };

  const handleVariantChange = (
    selectionId: string,
    variantId: string | number
  ) => {
    // Convert to string to ensure type safety
    const variantIdStr = String(variantId);

    setSelectedProducts((prev) =>
      prev.map((item) => {
        if (item.id === selectionId) {
          return {
            ...item,
            variant: variantIdStr ? Number(variantIdStr) : undefined,
          };
        }
        return item;
      })
    );
  };

  const handleQuantityChange = (selectionId: string, quantity: number) => {
    setSelectedProducts((prev) =>
      prev.map((item) => {
        if (item.id === selectionId) {
          return {
            ...item,
            quantity: quantity,
          };
        }
        return item;
      })
    );
  };

  // Custom total price functionality is not used in this UI version
  // but kept for future reference or if needed later

  const handleAdd = () => {
    const orderItems: OrderItem[] = selectedProducts.map(
      ({ product, variant: variantId, quantity, customTotalPrice }) => {
        const variant = variantId
          ? product.variants.find((v) => v.id === variantId)
          : null;

        return {
          id: 0,
          product: product.id,
          product_name: product.name,
          variant: variant?.id,
          variant_name: variant?.name,
          quantity,
          price: variant?.price || product.price || 0,
          total_price:
            customTotalPrice ||
            Math.round((variant?.price || product.price || 0) * quantity),
          unit: product.unit,
        };
      }
    );

    onAdd(orderItems); // Pass entire array instead of individual items
    onClose();
    setSelectedProducts([]);
  };

  const formatPrice = (price: number) => {
    return `${price.toLocaleString()} VND`;
  };

  const getVariantPrice = (product: Product, variantId?: number) => {
    if (!variantId) return product.price || 0;
    const variant = product.variants.find((v) => v.id === variantId);
    return variant?.price || product.price || 0;
  };

  return (
    <Modal
      title="Thêm sản phẩm"
      open={isOpen}
      onCancel={onClose}
      width={1200}
      footer={null}
      styles={{ body: { maxHeight: "80vh", overflow: "auto" } }}
    >
      <div style={{ marginBottom: 16 }}>
        <ProductSearch onSearch={setSearchParams} />
      </div>

      <Row gutter={16}>
        <Col span={12}>
          <Card
            title="Sản phẩm"
            styles={{
              body: { height: 500, overflow: "auto", padding: 0 },
            }}
          >
            <Spin spinning={loading}>
              {products.length > 0 ? (
                <List
                  dataSource={products}
                  renderItem={(product) => (
                    <List.Item
                      key={product.id}
                      onClick={() => handleProductClick(product)}
                      style={{
                        cursor: "pointer",
                        backgroundColor: selectedProducts.some(
                          (p) => p.product.id === product.id
                        )
                          ? "#e6f7ff"
                          : "transparent",
                        padding: "8px 16px",
                      }}
                      className="hover:bg-gray-50"
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            src={product.main_image}
                            shape="square"
                            size={48}
                            style={{ objectFit: "cover" }}
                          />
                        }
                        title={product.name}
                        description={formatPrice(
                          Math.round(product.price || 0)
                        )}
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty description="Không tìm thấy sản phẩm" />
              )}
            </Spin>
          </Card>
        </Col>

        <Col span={12}>
          <Card
            title="Sản phẩm đã chọn"
            styles={{
              body: { height: 500, overflow: "auto" },
            }}
            extra={
              selectedProducts.length > 0 ? (
                <Text type="secondary">{selectedProducts.length} sản phẩm</Text>
              ) : null
            }
          >
            {selectedProducts.length > 0 ? (
              <List
                dataSource={selectedProducts}
                renderItem={({ id, product, variant, quantity }) => (
                  <List.Item
                    key={id}
                    actions={[
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleRemoveProduct(id)}
                      />,
                    ]}
                  >
                    <List.Item.Meta
                      title={product.name}
                      description={
                        <Row gutter={8} align="middle">
                          <Col span={12}>
                            <Select
                              style={{ width: "100%" }}
                              value={variant || ""}
                              onChange={(value) =>
                                handleVariantChange(id, value)
                              }
                            >
                              <Option value="">
                                Mặc định -{" "}
                                {formatPrice(Math.round(product.price || 0))}
                              </Option>
                              {product.variants.map((v) => (
                                <Option key={v.id} value={v.id}>
                                  {v.name} -{" "}
                                  {formatPrice(Math.round(v.price || 0))}
                                </Option>
                              ))}
                            </Select>
                          </Col>
                          <Col span={6}>
                            <InputNumber
                              min={1}
                              value={quantity}
                              onChange={(value) =>
                                handleQuantityChange(id, value || 1)
                              }
                              style={{ width: "100%" }}
                            />
                          </Col>
                          <Col span={6}>
                            <Text>
                              {formatPrice(getVariantPrice(product, variant))}
                            </Text>
                          </Col>
                        </Row>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="Chọn sản phẩm để thêm vào đơn hàng" />
            )}

            {selectedProducts.length > 0 && (
              <>
                <Divider />
                <div style={{ textAlign: "right" }}>
                  <Button
                    type="primary"
                    icon={<ShoppingCartOutlined />}
                    onClick={handleAdd}
                    size="large"
                  >
                    Thêm vào đơn hàng ({selectedProducts.length} sản phẩm)
                  </Button>
                </div>
              </>
            )}
          </Card>
        </Col>
      </Row>
    </Modal>
  );
}
