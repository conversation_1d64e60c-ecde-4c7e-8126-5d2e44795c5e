import { STATUS_OPTIONS } from "@/types/order"; // Reverted: Order import removed
import { Tabs } from "antd";
import {
  Menu,
  CircleEllipsis,
  Package,
  Truck,
  Receipt,
  Ban,
} from "lucide-react";
import { TabsProps } from "antd";

interface OrderStatusTabsProps {
  currentStatus: string; // Reverted to string
  onChange: (status: string) => void; // Reverted to string
  counts?: Record<string, number>;
}

const statuses = [
  { value: "", label: "Tất cả", icon: Menu, color: undefined },
  ...STATUS_OPTIONS.map((status) => ({
    value: status.value,
    label: status.label,
    icon:
      status.value === "pending"
        ? CircleEllipsis
        : status.value === "processing"
        ? Package
        : status.value === "shipped"
        ? Truck
        : status.value === "delivered"
        ? Receipt
        : Ban,
    color:
      status.value === "pending"
        ? "#faad14"
        : status.value === "processing"
        ? "#1677ff"
        : status.value === "shipped"
        ? "#722ed1"
        : status.value === "delivered"
        ? "#52c41a"
        : status.value === "cancelled"
        ? "#ff4d4f"
        : undefined,
  })),
] as const;

export function OrderStatusTabs({
  currentStatus,
  onChange,
  counts,
}: OrderStatusTabsProps) {
  const items: TabsProps["items"] = statuses.map(
    ({ value, label, icon: Icon, color }) => ({
      key: value,
      label: (
        <span className="flex items-center gap-2" style={{ color }}>
          <Icon size={16} />
          <span>{label}</span>
          {counts?.[value] !== undefined && (
            <span className="ml-1 text-xs bg-muted px-2 py-0.5 rounded-full">
              {counts[value]}
            </span>
          )}
        </span>
      ),
    })
  );

  return (
    <Tabs
      activeKey={currentStatus}
      onChange={onChange}
      items={items}
      className="w-full bg-white rounded-md shadow-sm px-4"
    />
  );
}
