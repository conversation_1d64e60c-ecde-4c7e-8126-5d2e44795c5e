import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { Order } from "../../types/order";
import { KanbanCard } from "./KanbanCard";
import { Card } from "antd";
import {
  KanbanStatus,
  KANBAN_STATUS_OPTIONS,
  getKanbanStatusIcon,
  filterOrdersByKanbanStatus,
} from "./kanbanUtils";

interface KanbanListProps {
  orders: Order[];
  onDragEnd: (result: any) => void;
  statusCounts?: Record<string, number>;
  onStatusClick?: (status: string) => void;
}

export function KanbanList({
  orders,
  onDragEnd,
  statusCounts = {},
  onStatusClick,
}: KanbanListProps) {
  return (
    <div className="p-4">
      <DragDropContext onDragEnd={onDragEnd}>
        {/* Container with horizontal scroll */}
        <div className="overflow-x-auto pb-4">
          {/* Fixed width columns */}
          <div
            className="flex gap-4"
            style={{ minWidth: KANBAN_STATUS_OPTIONS.length * 320 + "px" }}
          >
            {KANBAN_STATUS_OPTIONS.map(({ value: status, label, color }) => (
              <div key={status} className="flex-none w-80">
                <Card
                  title={
                    <div
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => onStatusClick?.(status)}
                    >
                      {getKanbanStatusIcon(status)}
                      <span>{label}</span>
                      <span className="ml-auto bg-white text-gray-800 px-2 py-0.5 rounded-full text-sm">
                        {statusCounts[status] || 0}
                      </span>
                    </div>
                  }
                  className="w-full"
                  styles={{
                    header: {
                      background: color,
                      color: "#fff",
                    },
                    body: {
                      padding: "12px",
                      maxHeight: "calc(100vh - 300px)",
                      overflow: "hidden",
                    },
                  }}
                >
                  {/* Vertical scroll container */}
                  <div
                    className="overflow-y-auto"
                    style={{ maxHeight: "calc(100vh - 300px)" }}
                  >
                    <Droppable droppableId={status}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className="flex flex-col gap-4 min-h-[200px]"
                        >
                          {filterOrdersByKanbanStatus(
                            orders,
                            status as KanbanStatus
                          ).map((order, index) => (
                            <Draggable
                              key={order.id}
                              draggableId={order.id.toString()}
                              index={index}
                            >
                              {(provided) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                >
                                  <KanbanCard order={order} />
                                </div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </DragDropContext>
    </div>
  );
}
