interface OrderCardFooterProps {
  notes?: string;
  onViewDetails: () => void;
  isDesktop?: boolean;
}

export function OrderCardFooter({ notes, onViewDetails, isDesktop }: OrderCardFooterProps) {
  return (
    <div className={`${isDesktop ? "px-0" : "p-4"} space-y-2`}>
      <div className="text-muted-foreground text-sm">Ghi chú:</div>
      <div className="whitespace-pre-wrap text-sm">{notes || "—"}</div>
      <button
        onClick={onViewDetails}
          className={`${
            isDesktop 
              ? "text-primary hover:text-primary/90" 
              : "w-full mt-4 px-4 py-2.5 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
          } text-sm font-medium`}
      >
        Xem chi tiết
      </button>
    </div>
  );
}
