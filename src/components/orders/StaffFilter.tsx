import { Select } from "antd";
import { Staff } from "../../types/staff";
import { UserIcon, TruckIcon } from "@heroicons/react/24/outline";
import { useLocation } from "react-router-dom";
import { SHIPPING_UNIT_OPTIONS } from "../../types/order";

interface StaffFilterProps {
  userRole: string;
  salesAdmin?: Staff | null;
  deliveryStaff?: Staff | null;
  shippingUnit?: string | null;
  onSalesAdminChange: (staffId: number | null) => void;
  onDeliveryStaffChange: (staffId: number | null) => void;
  onShippingUnitChange?: (unit: string | null) => void;
  staffList: Staff[];
}

export function StaffFilter({
  userRole,
  salesAdmin,
  deliveryStaff,
  shippingUnit,
  onSalesAdminChange,
  onDeliveryStaffChange,
  onShippingUnitChange,
  staffList,
}: StaffFilterProps) {
  const location = useLocation();
  const isCustomersPage = location.pathname.startsWith("/customers");
  const isDashboardPage = location.pathname === "/";
  const isProductRevenuePage = location.pathname === "/reports/products";

  const salesStaffList = staffList.filter(
    (staff) => staff.role === "sales_admin" || staff.role === "sales_manager"
  );
  const deliveryStaffList = staffList.filter(
    (staff) => staff.role === "delivery_staff"
  );

  // Filter out the motorbike option from shipping unit options
  const filteredShippingUnitOptions = SHIPPING_UNIT_OPTIONS.filter(
    (option) => option.value !== "motorbike"
  );

  return (
    <div className="flex flex-wrap gap-4">
      {userRole !== "sales_admin" && (
        <div className="flex-1 min-w-[200px]">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
            <UserIcon className="h-4 w-4" />
            Nhân viên bán hàng
          </div>
          <Select
            value={salesAdmin?.id || null}
            onChange={onSalesAdminChange}
            className="w-full"
            size="large"
            options={[
              { value: null, label: "Tất cả" },
              ...salesStaffList.map((staff) => ({
                value: staff.id,
                label: `${staff.first_name} ${staff.last_name}${
                  staff.role === "sales_manager" ? " (Quản lý)" : ""
                }`,
              })),
            ]}
          />
        </div>
      )}

      {!isCustomersPage && !isDashboardPage && !isProductRevenuePage && (
        <>
          <div className="flex-1 min-w-[200px]">
            <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
              <UserIcon className="h-4 w-4" />
              Nhân viên giao hàng
            </div>
            <Select
              value={deliveryStaff?.id || null}
              onChange={onDeliveryStaffChange}
              className="w-full"
              size="large"
              options={[
                { value: null, label: "Tất cả" },
                ...deliveryStaffList.map((staff) => ({
                  value: staff.id,
                  label: `${staff.first_name} ${staff.last_name}`,
                })),
              ]}
            />
          </div>

          {onShippingUnitChange && (
            <div className="flex-1 min-w-[200px]">
              <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                <TruckIcon className="h-4 w-4" />
                Phương thức vận chuyển
              </div>
              <Select
                value={shippingUnit || null}
                onChange={onShippingUnitChange}
                className="w-full"
                size="large"
                options={[
                  { value: null, label: "Tất cả" },
                  ...filteredShippingUnitOptions.map((option) => ({
                    value: option.value,
                    label: option.label,
                  })),
                ]}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
