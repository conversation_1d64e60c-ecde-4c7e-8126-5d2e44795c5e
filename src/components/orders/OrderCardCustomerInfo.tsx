import {
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import { OrderCardSection } from "./OrderCardSection";
import { Order } from "../../types/order";

interface OrderCardCustomerInfoProps {
  order: Order;
}

function CustomerInfoContent({ order }: OrderCardCustomerInfoProps) {
  return (
    <div className="space-y-2">
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <UserIcon className="h-4 w-4" />
          Họ tên:
        </div>
        <div className="font-medium">{order.user.full_name}</div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <PhoneIcon className="h-4 w-4" />
          SĐT:
        </div>
        <div className="font-medium">{order.phone_number}</div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <EnvelopeIcon className="h-4 w-4" />
          Email:
        </div>
        <div>{order.email}</div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <MapPinIcon className="h-4 w-4" />
          Địa chỉ:
        </div>
        <div className="space-y-0.5">
          <div>{order.shipping_address}</div>
          {order.ward && <div>Phường: {order.ward}</div>}
          {order.district && <div>Quận: {order.district}</div>}
          {order.city && <div>Thành phố: {order.city}</div>}
        </div>
      </div>
    </div>
  );
}

interface Props extends OrderCardCustomerInfoProps {
  isDesktop?: boolean;
}

export function OrderCardCustomerInfo({ order, isDesktop }: Props) {
  if (isDesktop) {
    return <CustomerInfoContent order={order} />;
  }

  return (
    <OrderCardSection title="THÔNG TIN KHÁCH HÀNG" defaultOpen={false}>
      <CustomerInfoContent order={order} />
    </OrderCardSection>
  );
}
