import { useNavigate } from "react-router-dom";
import { Order } from "../../types/order";
import { useBreakpoint } from "../../hooks/useBreakpoint";
import { OrderCard } from "./OrderCard";
import { OrderTableView } from "./OrderTableView";
import { Staff } from "../../types/staff";

interface OrderTableProps {
  orders: Order[];
  staffList: Staff[];
  onUpdateStatus: (orderId: number, status: Order["status"]) => void;
  onUpdateDeliveryMethod: (
    orderId: number,
    params: { delivery_staff_id?: number | null; shipping_unit?: string }
  ) => void;
  onUpdatePaymentMethod?: (
    orderId: number,
    paymentMethod: "cod" | "cash" | "bank_transfer"
  ) => void;
  onUpdatePaymentStatus?: (
    orderId: number,
    paymentStatus: "paid" | "unpaid"
  ) => void;
}

export function OrderTable({
  orders,
  staffList,
  onUpdateStatus,
  onUpdateDeliveryMethod,
  onUpdatePaymentMethod,
  onUpdatePaymentStatus,
}: OrderTableProps) {
  const navigate = useNavigate();
  // const isDesktop = useBreakpoint("lg");

  // if (!isDesktop) {
  //   return (
  //     <div className="space-y-4">
  //       {orders.map((order) => (
  //         <OrderCard
  //           key={order.id}
  //           order={order}
  //           onUpdateStatus={onUpdateStatus}
  //           onViewDetails={(id) => navigate(`/orders/${id}`)}
  //         />
  //       ))}
  //     </div>
  //   );
  // }

  return (
    // <div className="hidden lg:block">
    <OrderTableView
      orders={orders}
      staffList={staffList}
      onUpdateStatus={onUpdateStatus}
      onUpdateDeliveryMethod={onUpdateDeliveryMethod}
      onUpdatePaymentMethod={onUpdatePaymentMethod || (() => {})}
      onUpdatePaymentStatus={onUpdatePaymentStatus}
      onViewDetails={(id) => navigate(`/orders/${id}`)}
    />
    // </div>
  );
}
