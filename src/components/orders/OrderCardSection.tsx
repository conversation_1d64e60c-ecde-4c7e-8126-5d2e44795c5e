import { useState } from "react";
import { ChevronUpIcon, ChevronDownIcon } from "@heroicons/react/24/outline";

interface OrderCardSectionProps {
  title: string;
  defaultOpen?: boolean;
  children: React.ReactNode;
}

export function OrderCardSection({ title, defaultOpen = false, children }: OrderCardSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="border-b last:border-b-0">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full p-4 text-left bg-muted/10"
      >
        <span className="font-medium text-sm">{title}</span>
        {isOpen ? (
          <ChevronUpIcon className="h-5 w-5 text-muted-foreground" />
        ) : (
          <ChevronDownIcon className="h-5 w-5 text-muted-foreground" />
        )}
      </button>
      {isOpen && <div className="p-4 pt-2">{children}</div>}
    </div>
  );
}
