import React from 'react';
import { Modal, Checkbox } from 'antd';

interface FieldSelectionModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  selectedFields: string[];
  onFieldsChange: (fields: string[]) => void;
  isExporting: boolean;
}

// Define available fields for export - keys should match how we'll retrieve data
export const availableExportFields = [
  { label: 'Mã ĐH', value: 'id' },
  { label: 'Ngày tạo', value: 'created_at' },
  { label: 'Khách hàng', value: 'customer_name' },
  { label: 'Số điện thoại', value: 'phone_number' },
  { label: 'Địa chỉ giao hàng', value: 'shipping_address' },
  { label: 'Sản phẩm (Tóm tắt)', value: 'items_summary' },
  { label: 'Phương thức TT', value: 'payment_method' },
  { label: 'Tổng tiền', value: 'final_total' },
  { label: 'Tr<PERSON>ng thái Đ<PERSON>', value: 'status' },
  { label: 'Trạng thái TT', value: 'payment_status' },
  { label: '<PERSON>hi chú', value: 'notes' }
];

const FieldSelectionModal: React.FC<FieldSelectionModalProps> = ({
  visible,
  onOk,
  onCancel,
  selectedFields,
  onFieldsChange,
  isExporting
}) => {
  return (
    <Modal
      title="Chọn các trường để xuất ra Excel"
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      okText="Xuất Excel"
      cancelText="Hủy"
      width={600}
      confirmLoading={isExporting}
    >
      <p className="mb-4">Vui lòng chọn các cột bạn muốn bao gồm trong file Excel:</p>
      <Checkbox.Group
        className="w-full"
        value={selectedFields}
        onChange={(checkedValues) => onFieldsChange(checkedValues as string[])}
      >
        <div className="grid grid-cols-2 gap-2">
          {availableExportFields.map(field => (
            <Checkbox key={field.value} value={field.value}>{field.label}</Checkbox>
          ))}
        </div>
      </Checkbox.Group>
    </Modal>
  );
};

export default FieldSelectionModal;