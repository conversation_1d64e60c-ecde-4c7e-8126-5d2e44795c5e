import { CalendarIcon, TruckIcon, UserIcon } from "@heroicons/react/24/outline";
import { OrderCardSection } from "./OrderCardSection";
import { Order } from "../../types/order";
import { statusColors } from "./orderUtils";
import { useAuth } from "../../context/auth-hooks";
import { STATUS_OPTIONS } from "../../types/order";

interface OrderCardShippingInfoProps {
  order: Order;
  onUpdateStatus: (orderId: number, status: Order["status"]) => void;
}

function ShippingInfoContent({
  order,
  onUpdateStatus,
}: OrderCardShippingInfoProps) {
  const { user } = useAuth();
  const isSalesManager = user?.role === "sales_manager";
  const isWarehouseStaff = user?.role === "warehouse_staff";
  const isDeliveryStaff = user?.role === "delivery_staff";

  const canUpdateToShipped = isWarehouseStaff && order.status === "processing";
  const canUpdateToDelivered = isDeliveryStaff && order.status === "shipped";
  const canUpdateToConfirmed = isSalesManager && order.status === "pending";

  const handleWarehouseUpdate = () => {
    onUpdateStatus(order.id, "shipped");
  };

  const handleDeliveryUpdate = () => {
    onUpdateStatus(order.id, "delivered");
  };
  return (
    <div className="space-y-2">
      {isSalesManager ? (
        <select
          value={order.status}
          onChange={(e) =>
            onUpdateStatus(order.id, e.target.value as Order["status"])
          }
          className={`w-full border rounded px-2 py-1 text-sm bg-background mb-2 ${
            statusColors[order.status]
          }`}
        >
          {STATUS_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
          ,
        </select>
      ) : (
        <div
          className={`w-full border rounded px-2 py-1 text-sm bg-background ${
            statusColors[order.status]
          }`}
        >
          {(() => {
            switch (order.status) {
              case "pending":
                return "Chờ xác nhận";
              case "processing":
                return "Đang xử lý";
              case "shipped":
                return "Đã gửi hàng";
              case "delivered":
                return "Đã giao hàng";
              case "cancelled":
                return "Đã hủy";
              default:
                return "—";
            }
          })()}
        </div>
      )}
      {canUpdateToConfirmed && (
        <button
          onClick={() => onUpdateStatus(order.id, "processing")}
          className="w-full bg-yellow-500 hover:bg-yellow-600 text-white rounded px-2 py-1 text-sm"
        >
          Đã xác nhận
        </button>
      )}

      {canUpdateToShipped && (
        <button
          onClick={handleWarehouseUpdate}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded px-2 py-1 text-sm"
        >
          Đã xuất kho
        </button>
      )}
      {canUpdateToDelivered && (
        <button
          onClick={handleDeliveryUpdate}
          className="w-full bg-green-500 hover:bg-green-600 text-white rounded px-2 py-1 text-sm"
        >
          Đã giao
        </button>
      )}
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <CalendarIcon className="h-4 w-4" />
          Ngày giao:
        </div>
        <div className="font-medium">
          {order.delivery_date
            ? new Date(order.delivery_date).toLocaleDateString("vi-VN")
            : "—"}
        </div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <TruckIcon className="h-4 w-4" />
          ĐVVC:
        </div>
        <div className="font-medium">
          {order.shipping_unit === "company_vehicle"
            ? "Xe Cty"
            : order.shipping_unit === "motorbike"
            ? "Xe máy"
            : order.shipping_unit === "grab"
            ? "Grab"
            : order.shipping_unit === "transport_partner"
            ? "Chành xe"
            : order.shipping_unit === "shipping_partner"
            ? "ĐVVC"
            : "—"}
        </div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <UserIcon className="h-4 w-4" />
          NV giao hàng:
        </div>
        <div className="font-medium">
          {order.delivery_staff
            ? `${order.delivery_staff.first_name} ${order.delivery_staff.last_name}`
            : "—"}
        </div>
      </div>
    </div>
  );
}

interface Props extends OrderCardShippingInfoProps {
  isDesktop?: boolean;
}

export function OrderCardShippingInfo({
  order,
  onUpdateStatus,
  isDesktop,
}: Props) {
  if (isDesktop) {
    return (
      <ShippingInfoContent order={order} onUpdateStatus={onUpdateStatus} />
    );
  }

  return (
    <OrderCardSection title="THÔNG TIN GIAO HÀNG" defaultOpen={false}>
      <ShippingInfoContent order={order} onUpdateStatus={onUpdateStatus} />
    </OrderCardSection>
  );
}
