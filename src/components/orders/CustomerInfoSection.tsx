import { Order } from "../../types/order";

interface CustomerInfoSectionProps {
  order: Order;
  isMobile?: boolean;
}

export function CustomerInfoSection({
  order,
  isMobile,
}: CustomerInfoSectionProps) {
  return (
    <div className="mb-6 bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-3">Thông tin khách hàng</h3>
      <div className={`grid ${isMobile ? "grid-cols-1" : "grid-cols-2"} gap-4`}>
        <div className={isMobile ? "" : "col-span-2"}>
          <p className="text-sm text-muted-foreground">Tên khách hàng</p>
          <p>{order.user.full_name}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Email</p>
          <p>{order.email}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground"><PERSON><PERSON> điện thoại</p>
          <p>{order.phone_number}</p>
        </div>
        <div className={isMobile ? "" : "col-span-2"}>
          <p className="text-sm text-muted-foreground">Địa chỉ giao hàng</p>
          <p>
            {[
              order.shipping_address,
              order.ward ? `Phường ${order.ward}` : null,
              order.district ? `Quận ${order.district}` : null,
              order.city ? `Thành phố ${order.city}` : null,
            ]
              .filter(Boolean)
              .join(", ")}
          </p>
        </div>
      </div>
    </div>
  );
}
