import { useState } from "react";
import { Input, Select, Button } from "antd";
import { DateRangePickerWithPresets } from "../common/DateRangePickerWithPresets";

interface OrderSearchProps {
  onSearch: (params: OrderSearchParams) => void;
  initialParams?: OrderSearchParams;
}

export interface OrderSearchParams {
  query?: string;
  searchBy: "id" | "email" | "phone";
  dateFrom?: string;
  dateTo?: string;
}

export function OrderSearch({ onSearch, initialParams }: OrderSearchProps) {
  const [searchParams, setSearchParams] = useState<OrderSearchParams>(() => ({
    searchBy: initialParams?.searchBy || "id",
    query: initialParams?.query,
    dateFrom: initialParams?.dateFrom,
    dateTo: initialParams?.dateTo,
  }));

  const handleSearch = () => {
    console.log(searchParams);
    onSearch(searchParams);
  };

  const handleReset = () => {
    setSearchParams({ searchBy: "id" });
    onSearch({ searchBy: "id" });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm đơn hàng..."
              value={searchParams.query || ""}
              onChange={(e) =>
                setSearchParams({ ...searchParams, query: e.target.value })
              }
              className="flex-1"
              size="large"
              onPressEnter={() => handleSearch()}
            />
            <Select
              value={searchParams.searchBy}
              onChange={(value) =>
                setSearchParams({
                  ...searchParams,
                  searchBy: value as OrderSearchParams["searchBy"],
                })
              }
              className="w-[140px]"
              size="large"
              options={[
                { value: "id", label: "Mã đơn hàng" },
                { value: "name", label: "Tên khách hàng" },
                { value: "email", label: "Email" },
                { value: "phone", label: "Số điện thoại" },
              ]}
            />
          </div>
        </div>

        <div className="min-w-[300px]">
          <DateRangePickerWithPresets
            size="large"
            value={[searchParams.dateFrom, searchParams.dateTo]}
            onChange={([from, to]) => {
              const newParams = {
                ...searchParams,
                dateFrom: from || undefined,
                dateTo: to || undefined,
              };
              setSearchParams(newParams);
              onSearch(newParams);
            }}
            className="w-full"
          />
        </div>

        <Button type="primary" size="large" onClick={handleSearch}>
          Tìm kiếm
        </Button>
        <Button size="large" onClick={handleReset}>
          Đặt lại
        </Button>
      </div>
    </div>
  );
}
