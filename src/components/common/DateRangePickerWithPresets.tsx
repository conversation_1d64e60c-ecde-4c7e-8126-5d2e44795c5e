import { DatePicker, Space } from "antd";
import dayjs from "dayjs";
import "dayjs/locale/vi";
import type { RangePickerProps } from "antd/es/date-picker";
import viVN from "antd/locale/vi_VN";

// Configure dayjs to use Vietnamese locale
dayjs.locale("vi");

interface DateRangePickerWithPresetsProps {
  value?: [string | undefined | null, string | undefined | null];
  onChange?: (
    dates: [string | undefined | null, string | undefined | null]
  ) => void;
  size?: "large" | "middle" | "small";
  className?: string;
}

export function DateRangePickerWithPresets({
  value,
  onChange,
  size = "middle",
  className,
}: DateRangePickerWithPresetsProps) {
  const presets: RangePickerProps["presets"] = [
    { label: "Hôm nay", value: [dayjs(), dayjs()] },
    {
      label: "Hôm qua",
      value: [dayjs().subtract(1, "d"), dayjs().subtract(1, "d")],
    },
    {
      label: "7 ngày trước",
      value: [dayjs().subtract(7, "d"), dayjs()],
    },
    {
      label: "30 ngày trước",
      value: [dayjs().subtract(30, "d"), dayjs()],
    },
    {
      label: "Tháng này",
      value: [dayjs().startOf("month"), dayjs().endOf("month")],
    },
  ];

  return (
    <DatePicker.RangePicker
      size={size}
      value={[
        value?.[0] ? dayjs(value[0]) : null,
        value?.[1] ? dayjs(value[1]) : null,
      ]}
      onChange={(dates) =>
        onChange?.(
          dates
            ? [
                dates[0]?.format("YYYY-MM-DD") || null,
                dates[1]?.format("YYYY-MM-DD") || null,
              ]
            : [null, null]
        )
      }
      className={className}
      presets={presets}
      locale={viVN.DatePicker}
      placeholder={["Từ ngày", "Đến ngày"]}
      format="DD/MM/YYYY"
    />
  );
}
