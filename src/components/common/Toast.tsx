import { useEffect } from "react";
import { CheckIcon, CloseIcon, InfoIcon } from "@/components/icons";

interface ToastProps {
  message: string;
  type: "success" | "error" | "info";
  onClose: () => void;
}

export function Toast({ message, type, onClose }: ToastProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 3000);

    return () => clearTimeout(timer);
  }, [onClose]);

  const baseClasses =
    "fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg transform transition-transform duration-300 ease-in-out";
  const typeClasses = {
    success: "bg-green-100 text-green-800 border border-green-200",
    error: "bg-red-100 text-red-800 border border-red-200",
    info: "bg-yellow-100 text-yellow-800 border border-yellow-200"
  }[type];

  return (
    <div className={`${baseClasses} ${typeClasses}`} role="alert">
      <div className="flex items-center gap-2">
        {{
          success: <CheckIcon />,
          error: <CloseIcon />,
          info: <InfoIcon />
        }[type]}
        <span>{message}</span>
      </div>
    </div>
  );
}
