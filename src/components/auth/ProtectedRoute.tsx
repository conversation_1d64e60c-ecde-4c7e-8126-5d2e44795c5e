import { ReactElement } from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/auth-hooks";
import { Button } from "antd";

interface ProtectedRouteProps {
  children: ReactElement;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, isLoading, logout } = useAuth();
  const navigate = useNavigate();

  // Show loading state while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <p className="text-muted-foreground">Đang tải...</p>
        </div>
      </div>
    );
  }

  const location = useLocation();

  if (!user) {
    const returnUrl = location.pathname + location.search;
    localStorage.setItem("returnUrl", returnUrl);
    return <Navigate to="/login" replace />;
  }

  // Check if user is admin
  if (!user.is_staff && !user.is_superuser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">
            Từ chối truy cập
          </h1>
          <p className="mt-2 text-muted-foreground">
            Bạn không có quyền truy cập vào khu vực này.
          </p>
          <Button
            onClick={() => {
              logout();
              navigate("/login", { replace: true });
            }}
            variant="solid"
            color="red"
            className="mt-4"
          >
            Đăng xuất
          </Button>
        </div>
      </div>
    );
  }

  return children;
}
