import { useState, useEffect, useCallback } from "react";
import { apiCall, endpoints } from "@/lib/api";
import { TopCustomerItem, TopCustomerResponse, OrderByOption, LimitOption } from "@/types/report";
import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import localeData from "dayjs/plugin/localeData";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";

// Configure dayjs plugins
dayjs.extend(weekday);
dayjs.extend(localeData);

interface UseTopCustomersReportProps {
  initialDateRange?: [string | undefined | null, string | undefined | null];
  initialOrderBy?: OrderByOption;
  initialLimitOption?: LimitOption;
  initialCustomLimit?: number;
}

interface UseTopCustomersReportReturn {
  // Data states
  loading: boolean;
  error: string | null;
  data: TopCustomerItem[];

  // Filter states
  dateRange: [string | undefined | null, string | undefined | null];
  orderBy: OrderByOption;
  limitOption: LimitOption;
  customLimit: number;

  // Filter setters
  setDateRange: (range: [string | undefined | null, string | undefined | null]) => void;
  setOrderBy: (orderBy: OrderByOption) => void;
  setLimitOption: (option: LimitOption) => void;
  setCustomLimit: (limit: number) => void;

  // Actions
  handleSearch: () => void;
  handleExportExcel: () => Promise<void>;

  // Computed values
  displayTitle: string;
  totalCount: number;
}

export function useTopCustomersReport({
  initialDateRange = [
    dayjs().subtract(30, "day").format("YYYY-MM-DD"),
    dayjs().format("YYYY-MM-DD"),
  ],
  initialOrderBy = "total_spent_desc",
  initialLimitOption = "top10",
  initialCustomLimit = 10,
}: UseTopCustomersReportProps = {}): UseTopCustomersReportReturn {

  // Data states
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<TopCustomerItem[]>([]);

  // Filter states
  const [dateRange, setDateRange] = useState<[string | undefined | null, string | undefined | null]>(initialDateRange);
  const [orderBy, setOrderBy] = useState<OrderByOption>(initialOrderBy);
  const [limitOption, setLimitOption] = useState<LimitOption>(initialLimitOption);
  const [customLimit, setCustomLimit] = useState<number>(initialCustomLimit);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      // Add date range if provided
      if (dateRange[0] && dateRange[1]) {
        params.set("dateFrom", dateRange[0]);
        params.set("dateTo", dateRange[1]);
      }

      // Add order by
      params.set("order_by", orderBy);

      // Add limit
      if (limitOption === "all") {
        params.set("limit", "all");
      } else if (limitOption.startsWith("top")) {
        params.set("limit", limitOption);
      } else {
        params.set("limit", customLimit.toString());
      }



      const response = await apiCall<TopCustomerResponse>(
        "GET",
        `${endpoints.reports.customerReport}?${params.toString()}`
      );

      if (response && Array.isArray(response.user_stats)) {
        // Add rank to each item
        const dataWithRank = response.user_stats.map((item, index) => ({
          ...item,
          rank: index + 1,
        }));
        setData(dataWithRank);
      } else {
        setError("Dữ liệu trả về không hợp lệ.");
        setData([]);
      }
    } catch (err: any) {
      console.error("Error fetching top customers data:", err);
      setError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [dateRange, orderBy, limitOption, customLimit]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleSearch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  const handleExportExcel = useCallback(async () => {
    if (data.length === 0) {
      return;
    }

    try {
      // Create workbook and worksheet
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("Top Khách Hàng");

      // Define columns
      worksheet.columns = [
        { header: "Hạng", key: "rank", width: 8 },
        { header: "Tên khách hàng", key: "user_name", width: 25 },
        { header: "Email", key: "email", width: 30 },
        { header: "Role", key: "role", width: 15 },
        { header: "Tổng doanh thu", key: "total_spent", width: 18 },
        { header: "Số đơn hàng", key: "total_orders", width: 12 },
        { header: "Giá trị đơn hàng TB", key: "average_order_value", width: 20 },
        { header: "Đơn hàng cuối", key: "last_order_date", width: 15 },
      ];

      // Add data rows
      data.forEach((customer, index) => {
        worksheet.addRow({
          rank: customer.rank || index + 1,
          user_name: customer.user_name,
          email: customer.email,
          role: customer.role,
          total_spent: customer.total_spent,
          total_orders: customer.total_orders,
          average_order_value: customer.total_orders > 0
            ? customer.total_spent / customer.total_orders
            : 0,
          last_order_date: customer.last_order_date
            ? dayjs(customer.last_order_date).format("DD/MM/YYYY")
            : "",
        });
      });

      // Style header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFE6F3FF" },
      };

      // Format currency columns
      worksheet.getColumn("total_spent").numFmt = "#,##0";
      worksheet.getColumn("average_order_value").numFmt = "#,##0";

      // Generate filename with date range
      const filename = `top-khach-hang-${dateRange[0]}-${dateRange[1]}.xlsx`;

      // Generate buffer and save file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      saveAs(blob, filename);
    } catch (error) {
      console.error("Error exporting Excel:", error);
    }
  }, [data, dateRange]);

  // Computed values
  const displayTitle =
    limitOption === "all"
      ? "Tất cả khách hàng"
      : limitOption.startsWith("top")
      ? `${limitOption.replace("top", "Top ")} khách hàng`
      : `Top ${customLimit} khách hàng`;

  const orderByLabel =
    orderBy === "total_spent_desc" ? "doanh thu cao nhất" :
    orderBy === "total_spent_asc" ? "doanh thu thấp nhất" :
    orderBy === "total_orders_desc" ? "đơn hàng nhiều nhất" :
    "đơn hàng ít nhất";



  return {
    // Data states
    loading,
    error,
    data,

    // Filter states
    dateRange,
    orderBy,
    limitOption,
    customLimit,

    // Filter setters
    setDateRange,
    setOrderBy,
    setLimitOption,
    setCustomLimit,

    // Actions
    handleSearch,
    handleExportExcel,

    // Computed values
    displayTitle: `${displayTitle} - Sắp xếp theo ${orderByLabel}`,
    totalCount: data.length,
  };
}
