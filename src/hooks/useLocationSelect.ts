import { useState, useEffect, useCallback, useRef } from 'react';
import { locationApi, LocationItem } from '../lib/locationApi';

interface UseLocationSelectProps {
  initialCity?: string;
  initialDistrict?: string;
  initialWard?: string;
  onCityChange?: (value: string, label: string) => void;
  onDistrictChange?: (value: string, label: string) => void;
  onWardChange?: (value: string, label: string) => void;
}

export function useLocationSelect({
  initialCity = '',
  initialDistrict = '',
  initialWard = '',
  onCityChange,
  onDistrictChange,
  onWardChange
}: UseLocationSelectProps = {}) {
  const [cities, setCities] = useState<LocationItem[]>([]);
  const [districts, setDistricts] = useState<LocationItem[]>([]);
  const [wards, setWards] = useState<LocationItem[]>([]);

  const [selectedCity, setSelectedCity] = useState<string>(initialCity);
  const [selectedDistrict, setSelectedDistrict] = useState<string>(initialDistrict);
  const [selectedWard, setSelectedWard] = useState<string>(initialWard);

  const [loadingCities, setLoadingCities] = useState(false);
  const [loadingDistricts, setLoadingDistricts] = useState(false);
  const [loadingWards, setLoadingWards] = useState(false);

  // Store callback functions in refs to prevent dependency changes
  const onCityChangeRef = useRef(onCityChange);
  const onDistrictChangeRef = useRef(onDistrictChange);
  const onWardChangeRef = useRef(onWardChange);

  // Update refs when callbacks change
  useEffect(() => {
    onCityChangeRef.current = onCityChange;
    onDistrictChangeRef.current = onDistrictChange;
    onWardChangeRef.current = onWardChange;
  }, [onCityChange, onDistrictChange, onWardChange]);

  // Track if a request is already in progress
  const fetchingCitiesRef = useRef(false);
  const fetchingDistrictsRef = useRef(false);
  const fetchingWardsRef = useRef(false);

  // Track previous values to prevent duplicate requests
  const prevCityRef = useRef(selectedCity);
  const prevDistrictRef = useRef(selectedDistrict);
  const prevWardRef = useRef(selectedWard);

  // Load cities on initial render
  useEffect(() => {
    const fetchCities = async () => {
      // Prevent duplicate requests
      if (fetchingCitiesRef.current || cities.length > 0) return;

      fetchingCitiesRef.current = true;
      setLoadingCities(true);

      try {
        const citiesData = await locationApi.getProvinces();
        setCities(citiesData);
      } catch (error) {
        console.error('Error fetching cities:', error);
      } finally {
        setLoadingCities(false);
        fetchingCitiesRef.current = false;
      }
    };

    fetchCities();
  }, [cities.length]);

  // Load districts when city changes
  useEffect(() => {
    // Skip if city hasn't changed or is empty
    if (!selectedCity || selectedCity === prevCityRef.current) {
      return;
    }

    prevCityRef.current = selectedCity;

    if (!selectedCity) {
      setDistricts([]);
      setSelectedDistrict('');
      return;
    }

    const fetchDistricts = async () => {
      // Prevent duplicate requests
      if (fetchingDistrictsRef.current) return;

      fetchingDistrictsRef.current = true;
      setLoadingDistricts(true);

      try {
        const districtsData = await locationApi.getDistricts(selectedCity);
        setDistricts(districtsData);

        // Find the city label
        const cityItem = cities.find(city => city.value === selectedCity);
        if (cityItem && onCityChangeRef.current) {
          onCityChangeRef.current(selectedCity, cityItem.label);
        }
      } catch (error) {
        console.error('Error fetching districts:', error);
      } finally {
        setLoadingDistricts(false);
        fetchingDistrictsRef.current = false;
      }
    };

    fetchDistricts();
  }, [selectedCity, cities]);

  // Load wards when district changes
  useEffect(() => {
    // Skip if district hasn't changed or is empty
    if (!selectedDistrict || selectedDistrict === prevDistrictRef.current) {
      return;
    }

    prevDistrictRef.current = selectedDistrict;

    if (!selectedDistrict) {
      setWards([]);
      setSelectedWard('');
      return;
    }

    const fetchWards = async () => {
      // Prevent duplicate requests
      if (fetchingWardsRef.current) return;

      fetchingWardsRef.current = true;
      setLoadingWards(true);

      try {
        const wardsData = await locationApi.getWards(selectedDistrict);
        setWards(wardsData);

        // Find the district label
        const districtItem = districts.find(district => district.value === selectedDistrict);
        if (districtItem && onDistrictChangeRef.current) {
          onDistrictChangeRef.current(selectedDistrict, districtItem.label);
        }
      } catch (error) {
        console.error('Error fetching wards:', error);
      } finally {
        setLoadingWards(false);
        fetchingWardsRef.current = false;
      }
    };

    fetchWards();
  }, [selectedDistrict, districts]);

  // Call onWardChange when ward changes
  useEffect(() => {
    // Skip if ward hasn't changed or is empty
    if (!selectedWard || selectedWard === prevWardRef.current) {
      return;
    }

    prevWardRef.current = selectedWard;

    if (selectedWard && onWardChangeRef.current) {
      const wardItem = wards.find(ward => ward.value === selectedWard);
      if (wardItem) {
        onWardChangeRef.current(selectedWard, wardItem.label);
      }
    }
  }, [selectedWard, wards]);

  const handleCityChange = useCallback((value: string) => {
    setSelectedCity(value);
    setSelectedDistrict('');
    setSelectedWard('');
  }, []);

  const handleDistrictChange = useCallback((value: string) => {
    setSelectedDistrict(value);
    setSelectedWard('');
  }, []);

  const handleWardChange = useCallback((value: string) => {
    setSelectedWard(value);
  }, []);

  return {
    cities,
    districts,
    wards,
    selectedCity,
    selectedDistrict,
    selectedWard,
    loadingCities,
    loadingDistricts,
    loadingWards,
    handleCityChange,
    handleDistrictChange,
    handleWardChange,
    setSelectedCity,
    setSelectedDistrict,
    setSelectedWard
  };
}
