import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Order } from "../types/order";
import { Staff } from "../types/staff";
import { apiCall, endpoints } from "../lib/api";
import { OrderSearchParams } from "../components/orders/OrderSearch";
import { useToast } from "../context/toast-hooks";
import {message} from "antd";

interface UseOrdersParams {
  userId?: number;
  userRole?: string;
  noPage?: boolean;
}

export function useOrders({ userId, userRole, noPage }: UseOrdersParams = {}) {
  const [urlSearchParams, setUrlSearchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  // State management for filters and pagination
  const [page, setPage] = useState(() => Number(urlSearchParams.get("page")) || 1);
  const [statusFilter, setStatusFilter] = useState<Order["status"] | "">(() => {
    const status = urlSearchParams.get("status");
    return (status as Order["status"] | "") || "";
  });

  const [searchParams, setSearchParams] = useState<OrderSearchParams>(() => ({
    searchBy: (urlSearchParams.get("searchBy") as OrderSearchParams["searchBy"]) || "id",
    query: urlSearchParams.get("query") || undefined,
    dateFrom: urlSearchParams.get("dateFrom") || undefined,
    dateTo: urlSearchParams.get("dateTo") || undefined,
  }));

  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<Staff | null>(() => {
    const id = urlSearchParams.get("sales_admin");
    return id ? ({ id: Number(id) } as Staff) : null;
  });

  const [selectedDeliveryStaff, setSelectedDeliveryStaff] = useState<Staff | null>(() => {
    const id = urlSearchParams.get("delivery_staff");
    return id ? ({ id: Number(id) } as Staff) : null;
  });

  // URL params sync
  useEffect(() => {
    const params = new URLSearchParams();

    if (statusFilter) {
      params.set("status", statusFilter);
    }

    if (searchParams.query) {
      params.set("query", searchParams.query);
      params.set("searchBy", searchParams.searchBy);
    }

    if (searchParams.dateFrom) {
      params.set("dateFrom", searchParams.dateFrom);
    }

    if (searchParams.dateTo) {
      params.set("dateTo", searchParams.dateTo);
    }

    if (page > 1) {
      params.set("page", String(page));
    }

    if (selectedSalesAdmin) {
      params.set("sales_admin", String(selectedSalesAdmin.id));
    }

    if (selectedDeliveryStaff) {
      params.set("delivery_staff", String(selectedDeliveryStaff.id));
    }

    setUrlSearchParams(params, { replace: true });
  }, [page, statusFilter, searchParams, selectedSalesAdmin, selectedDeliveryStaff, setUrlSearchParams]);

  // Fetch status counts
  const { data: statusCounts } = useQuery<Record<string, number>>({
    queryKey: [
      "orders",
      "status_counts",
      searchParams,
      selectedSalesAdmin?.id,
      selectedDeliveryStaff?.id,
    ],
    queryFn: async () => {
      let url = endpoints.orders.statusCounts;
      const params = new URLSearchParams();

      if (searchParams.query) {
        params.set("search", searchParams.query);
        params.set("search_by", searchParams.searchBy);
      }

      if (searchParams.dateFrom) {
        params.set("date_from", searchParams.dateFrom);
      }

      if (searchParams.dateTo) {
        params.set("date_to", searchParams.dateTo);
      }

      if (userRole === "sales_admin") {
        params.set("sales_admin", String(userId));
      } else if (selectedSalesAdmin) {
        params.set("sales_admin", String(selectedSalesAdmin.id));
      }

      if (selectedDeliveryStaff) {
        params.set("delivery_staff", String(selectedDeliveryStaff.id));
      }

      const queryString = params.toString();
      if (queryString) {
        url += "?" + queryString;
      }

      return apiCall("GET", url);
    },
  });

  // Fetch staff list
  const { data: staffList = [] } = useQuery<Staff[]>({
    queryKey: ["staff"],
    queryFn: async () => {
      const response = await apiCall<{ results: Staff[] }>(
        "GET",
        endpoints.customers.list + "?is_staff=true&no_page=true"
      );
      return response.results;
    },
  });

  // Fetch orders
  const { data: ordersData, isLoading } = useQuery<{ results: Order[]; count: number }>({
    queryKey: [
      "orders",
      page,
      statusFilter,
      searchParams,
      selectedSalesAdmin,
      selectedDeliveryStaff,
    ],
    queryFn: () => {
      let url = `${endpoints.orders.list}`;
      if (userRole === "sales_admin") {
        url += `/by_sales_admin/?id=${userId}`;
        if (!noPage) {
          url += `&page=${page}&page_size=10`;
        } else {
          url += "&no_page=true";
        }
      } else {
        url += "?";
        if (!noPage) {
          url += `page=${page}&page_size=10`;
        } else {
          url += "no_page=true";
        }
      }

      if (statusFilter) {
        url += `&status=${statusFilter}`;
      }

      if (searchParams.query) {
        url += `&search=${searchParams.query}&search_by=${searchParams.searchBy}`;
      }

      if (searchParams.dateFrom) {
        url += `&date_from=${searchParams.dateFrom}`;
      }

      if (searchParams.dateTo) {
        url += `&date_to=${searchParams.dateTo}`;
      }

      if (selectedSalesAdmin) {
        url += `&sales_admin=${selectedSalesAdmin.id}`;
      }

      if (selectedDeliveryStaff) {
        url += `&delivery_staff=${selectedDeliveryStaff.id}`;
      }

      return apiCall("GET", url);
    },
  });

  // Update order status
  const updateOrderStatus = async (orderId: number, status: Order["status"]) => {
    try {
      await apiCall("POST", endpoints.orders.updateStatus(orderId), { status });
      await queryClient.invalidateQueries({ queryKey: ["orders"] });
      message.success("Cập nhật trạng thái đơn hàng thành công");
    } catch (error) {
      console.error("Failed to update order status:", error);
      message.error("Cập nhật trạng thái đơn hàng thất bại");
    }
  };

  // Handler functions
  const handleSearch = (params: OrderSearchParams) => {
    setSearchParams(params);
    setPage(1);
  };

  const handleStatusChange = (status: string) => {
    setStatusFilter(status as Order["status"]);
    setPage(1);
  };

  const handleSalesAdminChange = (staffId: number | null) => {
    setSelectedSalesAdmin(staffId ? staffList.find((s) => s.id === staffId) || null : null);
    setPage(1);
  };

  const handleDeliveryStaffChange = (staffId: number | null) => {
    setSelectedDeliveryStaff(staffId ? staffList.find((s) => s.id === staffId) || null : null);
    setPage(1);
  };

  return {
    // States
    page,
    statusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedDeliveryStaff,
    
    // Data
    orders: ordersData?.results || [],
    totalCount: ordersData?.count || 0,
    statusCounts,
    staffList,
    isLoading,
    
    // Actions
    setPage,
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleDeliveryStaffChange,
    updateOrderStatus,
  };
}
