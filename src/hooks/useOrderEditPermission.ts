import { useAuth } from "../context/auth-hooks";
import { Order } from "../types/order";

export function useOrderEditPermission() {
  const { user } = useAuth();

  const canEditOrder = (order: Order): boolean => {
    if (!user) return false;

    // sales_manager can always edit
    if (user.role === 'sales_manager') return true;

    // sales_admin can only edit pending orders
    if (user.role === 'sales_admin') {
      return order.status === 'pending';
    }

    // other roles cannot edit
    return false;
  };

  return { canEditOrder };
}
