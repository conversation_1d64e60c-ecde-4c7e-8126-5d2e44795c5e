// src/hooks/useOrderImport.ts
import React, { useState, useCallback } from "react";
import <PERSON> from "papaparse";
import ExcelJS from "exceljs";
import { useAuth } from "@/context/auth-hooks";
import { useToast } from "@/context/toast-hooks";
import { api, endpoints, apiCall } from "@/lib/api";
import { Customer } from "@/types/customer";
import { User } from "@/types/auth";
import { PaginatedResponse, APIResponse } from "@/types/common"; // Giả sử bạn định nghĩa kiểu này
import {
  normalizeColumnName,
  removeConsecutiveEmptyRowsCorrected,
  mapPaymentMethod,
  mapShippingUnit,
  mapOrderStatus,
  parseAmount,
} from "@/utils/importHelper";
// import { EXPECTED_COLUMNS } from "@/utils/constants"; // Không dùng trực tiếp ở đây

interface ProcessedResult {
  success: boolean;
  message: string;
}

interface ImportedRow {
  [key: string]: string;
}

interface RowStatus {
  success?: boolean;
  message: string;
  loading?: boolean;
}

export const useOrderImport = () => {
  const { user } = useAuth(); // Vẫn cần user nếu logic cần thông tin user hiện tại
  const { showToast } = useToast();
  const [data, setData] = useState<ImportedRow[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [processedRows, setProcessedRows] = useState<{ [key: number]: RowStatus }>(
    {}
  );
  const [hasErrors, setHasErrors] = useState(false);

  // --- Các hàm gọi API (searchUser, createUser, ...) ---
  const searchUser = async (phone: string): Promise<Customer | null> => { 
    try {
          if (!phone || phone.trim() === '') {
            return null;
          }
          
          const response = await apiCall<PaginatedResponse<Customer>>(
            "GET",
            `${endpoints.customers.list}?role=customer&search=${phone}`
          );
    
          if (response.count > 0 && response.results.length > 0) {
            return response.results[0];
          }
          return null;
        } catch (error) {
          console.error("Error searching user:", error);
          return null;
    }
   };
  const createUser = async ( userData: any ): Promise<APIResponse<Customer> | null> => { 
    try {
          const response = await apiCall<APIResponse<Customer>>(
            "POST",
            endpoints.customers.createCustomer,
            userData
          );
          return response;
        } catch (error) {
          console.error("Error creating user:", error);
          return null;
        }
  };
  const searchSalesAdmin = async (name: string): Promise<Customer|null> => { 
     try {
          const response = await apiCall<PaginatedResponse<Customer>>(
            "GET",
            `${endpoints.staff.list}?role=sales_admin&search=${name}`
          );
          if (response.count > 0 && response.results.length > 0) {
            return response.results[0];
          }
        } catch (error) {
          console.error("Error searching sales admin:", error);
        }
        return null;
   };
  const createOrder = async ( orderData: any ): Promise<APIResponse<any> | null> => { 
    try {
          const response = await apiCall<APIResponse<any>>(
            "POST",
            endpoints.orders.create,
            orderData
          );
          return response;
        } catch (error) {
          console.error("Error creating order:", error);
          return null;
        }
   };
  // --- Kết thúc hàm API ---

  // --- Hàm xử lý logic ---
  const processRow = useCallback(async (row: ImportedRow, index: number) => {
    // Set loading state
    setProcessedRows((prev) => ({
      ...prev,
      [index]: {
        loading: true,
        message: "Đang xử lý đơn hàng..."
      }
    }));

    try {
        // Search for existing user or create new one
        const phone = row["Số Điện Thoại"]?.replace(/\s+/g, '').trim() || '';
        let customer = await searchUser(phone);
        // Debugging log removed: console.log("existing user:", customer);
        
        let customerId;
        if (!customer) {
        // Create new user
        const customerData = {
        first_name: row["Họ và Tên"],
        phone_number: phone,
        email: row["Email"] || `${phone}@placeholder.com`,
        shipping_address: row["Đường"],
        ward: row["Phường"],
        district: row["Quận"],
        city: row["Tỉnh/Thành phố"],
        };
        const newCustomer = await createUser(customerData);
        console.log("created user:", newCustomer);
        if (!newCustomer) {
        throw new Error("Tạo người dùng thất bại");
        }
        customerId = newCustomer.user.id;
        } else {
        customerId = customer.id;
        }
        
        // Get sales admin
        const salesAdminName = row["NVBH"];
        const salesAdmin = await searchSalesAdmin(salesAdminName);
        console.log("salesAdmin", salesAdmin);
        const salesAdminId = salesAdmin?.id || user?.id;
        
        // Prepare order data
        const orderData = {
        user: customerId,
        phone_number: phone,
        email: row["Email"] || `${phone}@placeholder.com`,
        shipping_address: row["Đường"],
        ward: row["Phường"],
        district: row["Quận"],
        city: row["Tỉnh/Thành phố"],
        payment_method: mapPaymentMethod(row["Phương Thức Thanh Toán"]),
        items: [
        {
        product: 1,
        quantity: 1,
        total_price: parseAmount(row["Giá Tiền"]),
        },
        ],
        shipping_unit: mapShippingUnit(row["Đơn Vị Vận Chuyển"]),
        shipping_fee: parseAmount(row["Phí VC"]),
        notes: row["Note"],
        sales_admin: salesAdminId,
        status: mapOrderStatus(row["Trạng Thái Đơn Hàng"]),
        };
        console.log(orderData);
        
        const order = await createOrder(orderData);
        if (!order) {
        throw new Error("Tạo đơn hàng thất bại");
        }
        
        setProcessedRows((prev) => ({
          ...prev,
          [index]: {
            success: true,
            message: "Đã tạo đơn hàng thành công"
          }
        }));
        return true;
        } catch (error: any) {
        setProcessedRows((prev) => ({
          ...prev,
          [index]: {
            success: false,
            message: error.message || "Không thể tạo đơn hàng"
          }
        }));
        setHasErrors(true);
        return false;
        }
  }, [showToast]); // Thêm dependencies nếu cần (ví dụ: các hàm API nếu chúng không được useCallback)

  const handleSaveOrders = useCallback(async () => {
      if (data.length === 0) return;
      setIsSaving(true);
      const results = [];
      const currentProcessed: { [key: number]: string } = {}; // Tạo object tạm để cập nhật 1 lần

      for (let i = 0; i < data.length; i++) {
          const success = await processRow(data[i], i);
          results.push(success);
       
      }

   

      const successCount = results.filter((r) => r).length;
      showToast(
          `Đã xử lý ${successCount}/${data.length} đơn hàng thành công`,
          "success"
      );
      setIsSaving(false);
  }, [data, processRow, showToast]);


  const downloadErrorRows = useCallback(async () => {
    const errorRows = data.filter((_, index) => 
      processedRows[index] && !processedRows[index].success
    );

    if (errorRows.length === 0) return;

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Đơn hàng lỗi');

    // Add headers
    const headers = Object.keys(errorRows[0]);
    worksheet.addRow(headers);

    // Add data rows
    errorRows.forEach((row) => {
      worksheet.addRow(Object.values(row));
    });

    // Generate blob and download
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `don_hang_loi_${new Date().toISOString().split('T')[0]}.xlsx`;
    a.click();
    window.URL.revokeObjectURL(url);
  }, [data, processedRows]);

  const processFile = useCallback(async (file: File) => {
    setProcessedRows({});
    setHasErrors(false);
    // ... (logic đọc file CSV/Excel như cũ) ...
    // Sử dụng các hàm từ importHelpers
    // Kết quả cuối cùng gọi setData(dataRows);
     if (file.name.endsWith(".csv")) {
          Papa.parse(file, {
            complete: (results: Papa.ParseResult<string[]>) => {
              const headers = results.data[0];
              const normalizedHeaders = headers.map(normalizeColumnName);
    
              const filteredRows = removeConsecutiveEmptyRowsCorrected(
                results.data.slice(1)
              );
              const rows = filteredRows.map((row) => {
                const normalizedRow: ImportedRow = {};
                headers.forEach((header, index) => {
                  const normalizedHeader = normalizedHeaders[index];
                  const value = row[index];
                  normalizedRow[normalizedHeader] = String(row[index]).trim();
                });
                return normalizedRow;
              });
    
              console.log(rows);
    
              setData(rows);
            },
            header: false,
          });
        } else {
          const workbook = new ExcelJS.Workbook();
    
          const arrayBuffer = await file.arrayBuffer();
    
          await workbook.xlsx.load(arrayBuffer);
    
          const worksheet = workbook.worksheets[0];
    
          if (!worksheet) {
            throw new Error("No worksheet found");
          }
    
          const allRows: (string | null)[][] = [];
          worksheet.eachRow({ includeEmpty: true }, (row) => {
            const rowValues = row.values as (string | null)[];
            allRows.push(
              rowValues
                .slice(1)
                .map((value) => (value === null ? "" : String(value)))
            );
          });
    
          // Get headers from first row
          const headers = allRows[0].map(String);
          console.log(headers);
    
          // Process remaining rows, removing consecutive empty rows
          const filteredRows = removeConsecutiveEmptyRowsCorrected(
            allRows.slice(1)
          );
          console.log(filteredRows);
          const normalizedHeaders = headers.map(normalizeColumnName);
    
          const dataRows = filteredRows.map((row) => {
            const normalizedRow: ImportedRow = {};
            headers.forEach((header, index) => {
              const normalizedHeader = normalizedHeaders[index];
              normalizedRow[normalizedHeader] = row[index]?.trim() || "";
            });
            return normalizedRow;
          });
    
          setData(dataRows);
        }
  }, [setData, setProcessedRows]); // Dependency

  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
          processFile(file);
      }
  }, [processFile]);

  const clearData = useCallback(() => {
      setData([]);
      setProcessedRows({});
  }, [setData, setProcessedRows]);

  return {
    data,
    isSaving,
    processedRows,
    hasErrors,
    handleFileChange,
    handleSaveOrders,
    clearData,
    downloadErrorRows,
  };
};
