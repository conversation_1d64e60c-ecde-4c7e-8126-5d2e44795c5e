import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Order } from "../types/order";
import { Staff } from "../types/staff";
import { apiCall, endpoints } from "../lib/api";
import { OrderSearchParams } from "../components/orders/OrderSearch";
import { message } from "antd";

interface UseOrdersBaseParams {
  userId?: number;
  userRole?: string;
  queryKey: string;
  noPage?: boolean;
  showroomOnly?: boolean;
}

export function useOrdersBase({ userId, userRole, queryKey, noPage, showroomOnly = false}: UseOrdersBaseParams) {
  const [urlSearchParams, setUrlSearchParams] = useSearchParams();
  const queryClient = useQueryClient();

  // State management for filters
  const [statusFilter, setStatusFilter] = useState<Order["status"] | "">(() => {
    const status = urlSearchParams.get("status");
    return (status as Order["status"] | "") || "";
  });

  const [searchParams, setSearchParams] = useState<OrderSearchParams>(() => ({
    searchBy: (urlSearchParams.get("searchBy") as OrderSearchParams["searchBy"]) || "id",
    query: urlSearchParams.get("query") || undefined,
    dateFrom: urlSearchParams.get("dateFrom") || undefined,
    dateTo: urlSearchParams.get("dateTo") || undefined,
  }));

  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<Staff | null>(() => {
    const id = urlSearchParams.get("sales_admin");
    return id ? ({ id: Number(id) } as Staff) : null;
  });

  const [selectedDeliveryStaff, setSelectedDeliveryStaff] = useState<Staff | null>(() => {
    const id = urlSearchParams.get("delivery_staff");
    return id ? ({ id: Number(id) } as Staff) : null;
  });

  const [selectedShippingUnit, setSelectedShippingUnit] = useState<string | null>(() => {
    const unit = urlSearchParams.get("shipping_unit");
    return unit || null;
  });

  const [paymentStatusFilter, setPaymentStatusFilter] = useState<Order['payment_status'][] | undefined>(() => {
    const statuses = urlSearchParams.get("payment_status");
    return statuses ? statuses.split(',') as Order['payment_status'][] : undefined;
  });

  const [showroomFilter, setShowroomFilter] = useState<boolean | undefined>(() => {
    const showroom = urlSearchParams.get("is_showroom");
    return showroom ? showroom === "true" : undefined;
  });

  useEffect(() => {
    if (showroomOnly) {
      setShowroomFilter(true);
    } else {
      const showroomParam = urlSearchParams.get("is_showroom");
      if (showroomParam !== null) {
        setShowroomFilter(showroomParam === "true");
      }
    }
  }, [showroomOnly, urlSearchParams]);

  // URL params sync
  useEffect(() => {
    const params = new URLSearchParams();

    if (statusFilter) {
      params.set("status", statusFilter);
    }

    if (searchParams.query) {
      params.set("query", searchParams.query);
      params.set("searchBy", searchParams.searchBy);
    }

    if (searchParams.dateFrom) {
      params.set("dateFrom", searchParams.dateFrom);
    }

    if (searchParams.dateTo) {
      params.set("dateTo", searchParams.dateTo);
    }

    if (selectedSalesAdmin) {
      params.set("sales_admin", String(selectedSalesAdmin.id));
    }

    if (selectedDeliveryStaff) {
      params.set("delivery_staff", String(selectedDeliveryStaff.id));
    }

    if (selectedShippingUnit) {
      params.set("shipping_unit", selectedShippingUnit);
    }

    if (paymentStatusFilter && paymentStatusFilter.length > 0) {
      params.set("payment_status", paymentStatusFilter.join(','));
    }

    if (showroomFilter !== undefined) {
      params.set("is_showroom", String(showroomFilter));
    }

    setUrlSearchParams(params, { replace: true });
  }, [statusFilter, searchParams, selectedSalesAdmin, selectedDeliveryStaff, selectedShippingUnit, paymentStatusFilter, setUrlSearchParams]);

  // Fetch status counts
  const { data: statusCounts } = useQuery<Record<string, number>>({
    queryKey: [
      queryKey,
      "status_counts",
      searchParams,
      selectedSalesAdmin?.id,
      selectedDeliveryStaff?.id,
      selectedShippingUnit,
      paymentStatusFilter,
      showroomFilter
    ],
    queryFn: async () => {
      let url = endpoints.orders.statusCounts;
      const params = new URLSearchParams();

      if (searchParams.query) {
        params.set("search", searchParams.query);
        params.set("search_by", searchParams.searchBy);
      }

      if (searchParams.dateFrom) {
        params.set("date_from", searchParams.dateFrom);
      }

      if (searchParams.dateTo) {
        params.set("date_to", searchParams.dateTo);
      }

      if (userRole === "sales_admin") {
        params.set("sales_admin", String(userId));
      } else if (selectedSalesAdmin) {
        params.set("sales_admin", String(selectedSalesAdmin.id));
      }

      if (selectedDeliveryStaff) {
        params.set("delivery_staff", String(selectedDeliveryStaff.id));
      }

      if (selectedShippingUnit) {
        params.set("shipping_unit", selectedShippingUnit);
      }

      if (paymentStatusFilter && paymentStatusFilter.length > 0) {
        params.set("payment_status", paymentStatusFilter.join(','));
      }

      params.set("is_showroom", showroomFilter ? "true": "false")

      const queryString = params.toString();
      if (queryString) {
        url += "?" + queryString;
      }

// Removed unnecessary console.log statement

      return apiCall("GET", url);
    },
  });

  // Fetch staff list
  const { data: staffList = [] } = useQuery<Staff[]>({
    queryKey: ["staff"],
    queryFn: async () => {
      const response = await apiCall<{ results: Staff[] }>(
        "GET",
        endpoints.customers.list + "?is_staff=true&no_page=true"
      );
      return response.results;
    },
  });

  // Fetch orders
  const getOrdersQueryFn = async (): Promise<{ results: Order[]; count: number }> => {
    let url = `${endpoints.orders.list}`;
    if (userRole === "sales_admin") {
      url += `/by_sales_admin/?id=${userId}`;
      if (!noPage) {
        url += "&page_size=10";
      } else {
        url += "&no_page=true";
      }
    } else {
      url += "?";
      if (!noPage) {
        url += "page_size=10";
      } else {
        url += "no_page=true";
      }
    }

    if (statusFilter) {
      url += `&status=${statusFilter}`;
    }

    if (searchParams.query) {
      url += `&search=${searchParams.query}&search_by=${searchParams.searchBy}`;
    }

    if (searchParams.dateFrom) {
      url += `&date_from=${searchParams.dateFrom}`;
    }

    if (searchParams.dateTo) {
      url += `&date_to=${searchParams.dateTo}`;
    }

    if (selectedSalesAdmin) {
      url += `&sales_admin=${selectedSalesAdmin.id}`;
    }

    if (selectedDeliveryStaff) {
      url += `&delivery_staff=${selectedDeliveryStaff.id}`;
    }

    if (selectedShippingUnit) {
      url += `&shipping_unit=${selectedShippingUnit}`;
    }

    if (paymentStatusFilter && paymentStatusFilter.length > 0) {
      url += `&payment_status=${paymentStatusFilter.join(',')}`;
    }

    // if (showroomFilter !== undefined) {
    url += `&is_showroom=${showroomFilter ? 'true' : 'false'}`;
    // }

    return apiCall("GET", url);
  };

  // Update order status
  const updateOrderStatus = async (
    orderId: number,
    status: Order["status"],
    additionalParams?: { notes?: string }
  ) => {
    try {
      const params = { status, ...additionalParams };
      await apiCall("POST", endpoints.orders.updateStatus(orderId), params);
      await queryClient.invalidateQueries({ queryKey: [queryKey] });
      message.success("Cập nhật trạng thái đơn hàng thành công");
    } catch (error) {
      console.error("Failed to update order status:", error);
      message.error("Cập nhật trạng thái đơn hàng thất bại");
    }
  };

  // Update delivery method (delivery staff or shipping unit)
  const updateDeliveryMethod = async (
    orderId: number,
    params: {
      delivery_staff_id?: number | null;
      shipping_unit?: string;
      notes?: string;
    }
  ) => {
    try {
      // Use the regular update endpoint instead of updateStatus
      await apiCall("PUT", endpoints.orders.update(orderId), params);
      await queryClient.invalidateQueries({ queryKey: [queryKey] });
      message.success("Cập nhật phương thức giao hàng thành công");
    } catch (error) {
      console.error("Failed to update delivery method:", error);
      message.error("Cập nhật phương thức giao hàng thất bại");
    }
  };

  // Update payment method and status
  const updatePaymentMethod = async (
    orderId: number,
    paymentMethod: "cod" | "cash" | "bank_transfer",
    additionalParams?: { payment_status?: "paid" | "unpaid"; notes?: string }
  ) => {
    try {
      // Use the regular update endpoint
      const params = {
        payment_method: paymentMethod,
        ...additionalParams
      };
      await apiCall("PUT", endpoints.orders.update(orderId), params);
      await queryClient.invalidateQueries({ queryKey: [queryKey] });
      message.success("Cập nhật phương thức thanh toán thành công");
    } catch (error) {
      console.error("Failed to update payment method:", error);
      message.error("Cập nhật phương thức thanh toán thất bại");
    }
  };

  // Update payment status
  const updatePaymentStatus = async (
    orderId: number,
    paymentStatus: "paid" | "unpaid",
    additionalParams?: { notes?: string }
  ) => {
    try {
      // Use the regular update endpoint
      const params = {
        payment_status: paymentStatus,
        ...additionalParams
      };
      await apiCall("PUT", endpoints.orders.update(orderId), params);
      await queryClient.invalidateQueries({ queryKey: [queryKey] });
      message.success("Cập nhật trạng thái thanh toán thành công");
    } catch (error) {
      console.error("Failed to update payment status:", error);
      message.error("Cập nhật trạng thái thanh toán thất bại");
    }
  };

  // Handler functions
  const handleSearch = (params: OrderSearchParams) => {
    setSearchParams(params);
  };

  const handleStatusChange = (status: string) => { // Parameter type changed to string
    // Type assertion happens here, which is safe as values come from defined tabs
    setStatusFilter(status as Order["status"] | "");
  };

  const handleSalesAdminChange = (staffId: number | null) => {
    setSelectedSalesAdmin(staffId ? staffList.find((s) => s.id === staffId) || null : null);
  };

  const handleDeliveryStaffChange = (staffId: number | null) => {
    setSelectedDeliveryStaff(staffId ? staffList.find((s) => s.id === staffId) || null : null);
  };

  const handleShippingUnitChange = (unit: string | null) => {
    setSelectedShippingUnit(unit);
  };

  const handlePaymentStatusFilterChange = (statuses?: Order['payment_status'][]) => {
    setPaymentStatusFilter(statuses);
  };

  return {
    // States
    statusFilter,
    paymentStatusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedDeliveryStaff,
    selectedShippingUnit,

    // Data
    statusCounts,
    staffList,

    // Actions
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleDeliveryStaffChange,
    handleShippingUnitChange,
    handlePaymentStatusFilterChange,
    showroomFilter,
    handleShowroomFilterChange: (value?: boolean) => setShowroomFilter(value),
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
    updatePaymentStatus,

    // Utils
    getOrdersQueryFn,
  };
}
