/**
 * Defines standard navigation paths used throughout the application.
 */
export const NAVIGATION_PATHS = {
  HOME: '/',
  LOGIN: '/login',

  DASHBOARD: '/dashboard', // Assuming a general dashboard path

  ORDERS: '/orders',
  ORDER_CREATE: '/orders/create',
  ORDER_DETAIL: (id: string | number) => `/orders/${id}`,
  ORDER_EXPORT: '/orders/export',
  ORDER_IMPORT: '/orders/import',
  ORDER_KANBAN: '/orders/kanban',
  ORDER_CONFIRM: (id: string | number) => `/orders/confirm/${id}`,

  CUSTOMERS: '/customers',
  CUSTOMER_CREATE: '/customers/create',
  CUSTOMER_DETAIL: (id: string | number) => `/customers/${id}`,

  PRODUCTS: '/products',
  PRODUCT_CREATE: '/products/create',
  PRODUCT_DETAIL: (id: string | number) => `/products/${id}`,

  STAFF: '/staff',
  STAFF_CREATE: '/staff/create',
  STAFF_DETAIL: (id: string | number) => `/staff/${id}`,

  REPORTS_REVENUE: '/reports/revenue',
  REPORTS_PRODUCTS: '/reports/products',
  REPORTS_CUSTOMER: '/reports/customer',
  REPORTS_DELIVERY: '/reports/delivery',
  REPORTS_TOP_CUSTOMERS: '/reports/top-customers',
};

/**
 * Generates a breadcrumb list for a given path.
 * This is a placeholder and might need a more sophisticated implementation
 * depending on the routing library and desired complexity.
 */
export const generateBreadcrumbs = (path: string) => {
  const parts = path.split('/').filter(part => part);
  const breadcrumbs = [{ label: 'Trang chủ', path: NAVIGATION_PATHS.HOME }];
  let currentPath = '';
  parts.forEach(part => {
    currentPath += `/${part}`;
    // This is a very basic mapping. Needs to be more robust.
    // For example, map 'orders' to 'Đơn hàng', 'create' to 'Tạo mới', [id] to 'Chi tiết' etc.
    let label = part.charAt(0).toUpperCase() + part.slice(1);
    if (part === 'orders') label = 'Đơn hàng';
    if (part === 'export') label = 'Xuất file';
    // Add more specific labels as needed
    breadcrumbs.push({ label, path: currentPath });
  });
  return breadcrumbs;
};
