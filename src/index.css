@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    /* Deep blue theme */
    --navy-50: 220 100% 95%;
    --navy-100: 220 95% 90%;
    --navy-200: 220 90% 80%;
    --navy-300: 220 85% 70%;
    --navy-400: 220 80% 55%;
    --navy-500: 220 85% 40%;
    --navy-600: 220 90% 30%;
    --navy-700: 223 90% 25%;
    --navy-800: 225 90% 20%;
    --navy-900: 225 95% 15%;
    --navy-950: 228 100% 10%;

    /* Base colors */
    --background: 220 30% 98%;
    --foreground: 225 80% 5%;

    --card: 0 0% 100%;
    --card-foreground: 225 80% 5%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 225 80% 5%;
 
    --primary: 220 85% 40%;
    --primary-foreground: 210 20% 98%;
 
    --secondary: 220 30% 96%;
    --secondary-foreground: 220 85% 40%;
 
    --muted: 220 30% 96%;
    --muted-foreground: 220 30% 40%;
 
    --accent: 220 30% 96%;
    --accent-foreground: 220 85% 40%;
 
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 30% 90%;
    --input: 220 30% 90%;
    --ring: 220 85% 40%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 225 80% 5%;
    --foreground: 210 20% 98%;
 
    --card: 225 80% 5%;
    --card-foreground: 210 20% 98%;
 
    --popover: 225 80% 5%;
    --popover-foreground: 210 20% 98%;
 
    --primary: 220 85% 40%;
    --primary-foreground: 210 20% 98%;
 
    --secondary: 220 50% 15%;
    --secondary-foreground: 210 20% 98%;
 
    --muted: 220 50% 15%;
    --muted-foreground: 220 20% 70%;
 
    --accent: 220 50% 15%;
    --accent-foreground: 210 20% 98%;
 
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 20% 98%;
 
    --border: 220 50% 15%;
    --input: 220 50% 15%;
    --ring: 220 85% 40%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
