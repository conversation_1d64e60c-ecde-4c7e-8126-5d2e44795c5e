import { format } from "date-fns";


export const mapPaymentMethod = (method: string): string => {
  const mapping: { [key: string]: string } = {
    "Tiền mặt": "cash",
    "Chuyển khoản": "bank_transfer",
    "Ship cod": "cod",
  };
  return mapping[method] || "cash";
};

export const mapShippingUnit = (unit: string): string => {
  const mapping: { [key: string]: string } = {
    "Xe Cty": "company_vehicle",
    "Xe máy": "motorbike",
    Grab: "grab",
    "Chành xe": "transport_partner",
    ĐVVC: "shipping_partner",
  };
  return mapping[unit] || "company_vehicle";
};

export const mapOrderStatus = (status: string): string => {
  const mapping: { [key: string]: string } = {
    "Đang Chuẩn Bị": "processing",
    "Đang Giao": "shipped",
    "Hoàn Thành": "delivered",
  };
  return mapping[status] || "processing";
};

export const parseAmount = (amount: string): number => {
  // Remove VND, dots, commas and spaces
  const cleanAmount = amount.replace(/[^0-9.-]+/g, "");
  return parseFloat(cleanAmount)*1000 || 0;
};

export const formatDate = (dateStr: string) => {
  try {
    const date = new Date(dateStr);
    return format(date, "dd/MM");
  } catch {
    return dateStr;
  }
};

export const formatCurrencyWith3Zeros = (value: string) => {
  if (!value) return "";
  const num = parseFloat(value) * 1000;
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(num);
};

export const formatCurrency = (value: string) => {
  if (!value) return "";
  const num = parseFloat(value);
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(num);
};

export const normalizeColumnName = (name: string): string => {
    const normalized = name.toLowerCase().trim();
    const columnMap: { [key: string]: string } = {
      "ho ten": "Họ và Tên",
      "ho va ten": "Họ và Tên",
      sdt: "Số Điện Thoại",
      "so dien thoai": "Số Điện Thoại",
      duong: "Đường",
      "dia chi": "Đường",
      phuong: "Phường",
      quan: "Quận",
      tp: "Tỉnh/Thành phố",
      tinh: "Tỉnh/Thành phố",
      "thanh pho": "Tỉnh/Thành phố",
      ngay: "Ngày Order",
      "ngay dat": "Ngày Order",
      nvbh: "NVBH",
      "nhan vien ban hang": "NVBH",
      "san pham": "Sản Phẩm",
      sp: "Sản Phẩm",
      gia: "Giá Tiền",
      "gia tien": "Giá Tiền",
      "phi van chuyen": "Phí VC",
      "phi vc": "Phí VC",
      "doanh so": "Doanh số",
      ds: "Doanh số",
      pttt: "Phương Thức Thanh Toán",
      "thanh toan": "Phương Thức Thanh Toán",
      "ngay giao": "Ngày Giao",
      dvvc: "Đơn Vị Vận Chuyển",
      "van chuyen": "Đơn Vị Vận Chuyển",
      "trang thai": "Trạng Thái Đơn Hàng",
      status: "Trạng Thái Đơn Hàng",
      shipper: "Nhân viên giao hàng",
      "nhan vien giao": "Nhân viên giao hàng",
      "ghi chu": "Note",
    };
  
    return columnMap[normalized] || name;
  };
  
export const isEmptyRow = (row: any[]): boolean => {
    return row.every((cell) => !cell || String(cell).trim() === "");
  };
  
export const removeConsecutiveEmptyRowsCorrected = (rows: any[]): any[] => {
    const result: any[] = []; // Mảng chứa kết quả cuối cùng (các hàng được giữ lại)
    const emptyRowBuffer: any[] = []; // Bộ đệm để lưu trữ các hàng trống liên tiếp
  
    for (let i = 0; i < rows.length; i++) {
      const currentRow = rows[i];
  
      if (isEmptyRow(currentRow)) {
        // Nếu là hàng trống, thêm vào bộ đệm
        emptyRowBuffer.push(currentRow);
      } else {
        // Nếu là hàng không trống:
        // 1. Xử lý bộ đệm hàng trống trước đó (nếu có)
        // Chỉ thêm các hàng trống trong bộ đệm vào kết quả
        // nếu số lượng của chúng ÍT HƠN 5
        if (emptyRowBuffer.length > 0 && emptyRowBuffer.length < 5) {
          // Sử dụng spread syntax để thêm các phần tử từ buffer vào result
          result.push(...emptyRowBuffer);
        }
        // 2. Xóa bộ đệm hàng trống (chuỗi trống đã bị ngắt bởi hàng không trống hiện tại)
        emptyRowBuffer.length = 0; // Cách hiệu quả để làm rỗng mảng
  
        // 3. Thêm hàng không trống hiện tại vào kết quả
        result.push(currentRow);
      }
    }
  
    // Sau khi vòng lặp kết thúc, kiểm tra xem có còn hàng trống nào trong bộ đệm ở cuối mảng không
    // và thêm chúng vào kết quả nếu số lượng ÍT HƠN 5
    if (emptyRowBuffer.length > 0 && emptyRowBuffer.length < 5) {
      result.push(...emptyRowBuffer);
    }
  
    return result;
  };
  
