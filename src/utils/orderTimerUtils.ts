import { apiCall, endpoints } from "../lib/api";

/**
 * Reset timer for all processing orders.
 * This function should be called at 3PM to reset all timers.
 * @returns Promise with the response data
 */
export const resetOrderTimers = async (): Promise<any> => {
  try {
    return await apiCall('POST', endpoints.orders.reset_time);
  } catch (error) {
    console.error('Error resetting order timers:', error);
    throw error;
  }
};

/**
 * Check if current time is outside working hours (before 8AM or after 3PM)
 * @returns boolean
 */
export const isOutsideWorkingHours = (): boolean => {
  const now = new Date();
  const currentHour = now.getHours();
  return currentHour >= 15 || currentHour < 8;
};

/**
 * Calculate elapsed time between two dates, only counting working hours (8AM-3PM)
 * @param start Start date
 * @param end End date
 * @returns Elapsed time in milliseconds
 */
export const calculateElapsedTime = (start: Date, end: Date): number => {
  // If outside working hours, return 0
  if (isOutsideWorkingHours()) {
    return 0;
  }

  // Calculate total milliseconds between dates
  const totalMs = end.getTime() - start.getTime();

  // If start date is after end date, return 0
  if (totalMs <= 0) {
    return 0;
  }

  return totalMs;
};

/**
 * Format elapsed time in milliseconds to "X hours Y minutes" string
 * @param elapsedMs Elapsed time in milliseconds
 * @returns Formatted string
 */
export const formatElapsedTime = (elapsedMs: number): string => {
  if (elapsedMs <= 0) {
    return "0 phút";
  }

  const hours = Math.floor(elapsedMs / (3600 * 1000));
  const minutes = Math.floor((elapsedMs % (3600 * 1000)) / (60 * 1000));

  if (hours > 0) {
    return `${hours} giờ ${minutes} phút`;
  } else {
    return `${minutes} phút`;
  }
};

/**
 * Kiểm tra xem đơn hàng có bị quá hạn không (quá 3 giờ)
 * @param confirmationTime Thời gian xác nhận đơn hàng
 * @param overdueLimitHours Giới hạn thời gian quá hạn (mặc định là 3 giờ)
 * @returns boolean - true nếu đơn hàng quá hạn
 */
export const isOrderOverdue = (
  confirmationTime: string | Date | null | undefined,
  overdueLimitHours: number = 3
): boolean => {
  // Nếu không có thời gian xác nhận hoặc đang ngoài giờ làm việc, không tính là quá hạn
  if (!confirmationTime || isOutsideWorkingHours()) {
    return false;
  }

  // Chuyển đổi thời gian xác nhận thành Date object nếu cần
  const confirmationDate = typeof confirmationTime === 'string'
    ? new Date(confirmationTime)
    : confirmationTime;

  // Tính thời gian đã trôi qua
  const now = new Date();
  const elapsedMs = calculateElapsedTime(confirmationDate, now);

  // Kiểm tra xem thời gian đã trôi qua có vượt quá giới hạn không
  return elapsedMs > overdueLimitHours * 60 * 60 * 1000;
};
