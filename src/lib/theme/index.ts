/**
 * Theme utilities for the application
 * Provides consistent access to colors and theme variables
 */

// Navy blue color palette
export const navyColors = {
  50: "hsl(var(--navy-50))",
  100: "hsl(var(--navy-100))",
  200: "hsl(var(--navy-200))",
  300: "hsl(var(--navy-300))",
  400: "hsl(var(--navy-400))",
  500: "hsl(var(--navy-500))", // Primary navy
  600: "hsl(var(--navy-600))",
  700: "hsl(var(--navy-700))",
  800: "hsl(var(--navy-800))",
  900: "hsl(var(--navy-900))",
  950: "hsl(var(--navy-950))",
} as const;

// Theme colors
export const themeColors = {
  // Base
  background: "hsl(var(--background))",
  foreground: "hsl(var(--foreground))",
  
  // UI Elements
  primary: {
    DEFAULT: "hsl(var(--primary))",
    foreground: "hsl(var(--primary-foreground))",
  },
  secondary: {
    DEFAULT: "hsl(var(--secondary))",
    foreground: "hsl(var(--secondary-foreground))",
  },
  muted: {
    DEFAULT: "hsl(var(--muted))",
    foreground: "hsl(var(--muted-foreground))",
  },
  accent: {
    DEFAULT: "hsl(var(--accent))",
    foreground: "hsl(var(--accent-foreground))",
  },
  
  // States
  destructive: {
    DEFAULT: "hsl(var(--destructive))",
    foreground: "hsl(var(--destructive-foreground))",
  },
  
  // Components
  card: {
    DEFAULT: "hsl(var(--card))",
    foreground: "hsl(var(--card-foreground))",
  },
  popover: {
    DEFAULT: "hsl(var(--popover))",
    foreground: "hsl(var(--popover-foreground))",
  },
  
  // Inputs
  border: "hsl(var(--border))",
  input: "hsl(var(--input))",
  ring: "hsl(var(--ring))",
} as const;

// Export combined theme
export const theme = {
  colors: {
    ...themeColors,
    navy: navyColors,
  },
  radius: "var(--radius)",
} as const;

/**
 * Helper function to get color with opacity
 * @param variable CSS variable name
 * @param opacity Opacity value (0-100)
 * @returns HSL color with opacity
 */
export const withOpacity = (variable: string, opacity: number): string => {
  return `hsl(var(${variable}) / ${opacity}%)`;
};

export default theme; 