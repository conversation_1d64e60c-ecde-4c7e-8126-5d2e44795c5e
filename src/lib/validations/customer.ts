import { z } from "zod";

export const customerFormSchema = z.object({
  first_name: z
    .string()
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must be less than 50 characters"),
  last_name: z
    .string()
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must be less than 50 characters")
    .optional(),
  email: z
    .string()
    .email("Invalid email address")
    .max(100, "Email must be less than 100 characters")
    .optional(),
  phone_number: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .max(15, "Phone number must be less than 15 digits")
    .regex(/^[0-9+()-\s]*$/, "Invalid phone number format")
    .optional()
    .nullable(),
  shipping_address: z
    .string()
    .max(500, "Address must be less than 500 characters")
    .optional()
    .nullable(),
  ward: z
    .string()
    .max(100, "Ward must be less than 100 characters")
    .optional()
    .nullable(),
  district: z
    .string()
    .max(100, "District must be less than 100 characters")
    .optional()
    .nullable(),
  city: z
    .string()
    .max(100, "City must be less than 100 characters")
    .optional()
    .nullable(),
});

export type CustomerFormData = z.infer<typeof customerFormSchema>;
