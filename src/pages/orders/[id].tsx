import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router-dom";
import { apiCall, endpoints } from "../../lib/api";
import { useAuth } from "../../context/auth-hooks";
import { Button, Select, message } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { useOrderEditPermission } from "../../hooks/useOrderEditPermission";
import { Order, OrderItem, STATUS_OPTIONS } from "../../types/order";
import { Staff } from "../../types/staff";
import ProductSelectionDialog from "../../components/orders/ProductSelectionDialog";
import { StaffSearchDialog } from "../../components/staff/StaffSearchDialog";
import { CustomerInfoSection } from "../../components/orders/CustomerInfoSection";
import { OrderItemsSection } from "../../components/orders/OrderItemsSection";
import { ShippingInfoSection } from "../../components/orders/ShippingInfoSection";
import { PaymentInfoSection } from "../../components/orders/PaymentInfoSection";
import { StaffAssignmentSection } from "../../components/orders/StaffAssignmentSection";
import { NotesSection } from "../../components/orders/NotesSection";
import { CancellationReasonModal } from "../../components/orders/CancellationReasonModal";
import { useToast } from "../../context/toast-hooks";
import { useIsMobile } from "@/hooks/useBreakpoint";
import { SaveOutlined } from "@ant-design/icons";

const statusColors = {
  pending: "text-yellow-600",
  processing: "text-blue-600",
  shipped: "text-purple-600",
  delivered: "text-green-600",
  cancelled: "text-destructive",
  returned: "text-orange-600",
  refunded: "text-red-600",
} as const;

export default function OrderDetailPage(): JSX.Element {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const { canEditOrder } = useOrderEditPermission();
  const { user } = useAuth();
  const isMobile = useIsMobile();

  const { data: order, isLoading } = useQuery<Order>({
    queryKey: ["order", id],
    queryFn: () => apiCall("GET", endpoints.orders.detail(Number(id))),
  });

  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const [isSalesDialogOpen, setIsSalesDialogOpen] = useState(false);
  const [isDeliveryDialogOpen, setIsDeliveryDialogOpen] = useState(false);
  const [isCancellationModalOpen, setIsCancellationModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<number | null>(null);

  // State to track all changes
  const [pendingChanges, setPendingChanges] = useState<Partial<Order>>({});
  const [selectedStatus, setSelectedStatus] = useState<Order["status"] | null>(
    null
  );
  const [pendingStatus, setPendingStatus] = useState<Order["status"] | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);

  // Set initial status when order data is loaded
  useEffect(() => {
    if (order) {
      setSelectedStatus(order.status);
    }
  }, [order]);

  // Function to collect changes instead of making API calls
  const updateOrder = useCallback((data: Partial<Order>) => {
    setPendingChanges((prev) => ({
      ...prev,
      ...data,
    }));
  }, []);

  // Function to save all collected changes
  const saveChanges = async () => {
    if (
      Object.keys(pendingChanges).length === 0 &&
      selectedStatus === order?.status
    ) {
      showToast("Không có thay đổi để lưu", "success");
      return;
    }

    try {
      setIsSaving(true);

      // Combine status change with other pending changes
      const dataToSave: Partial<Order> = {
        ...pendingChanges,
      };

      // Add status if it has changed
      if (selectedStatus !== order?.status) {
        dataToSave.status = selectedStatus as Order["status"];
      }

      // Ensure notes field is included
      if (pendingChanges.notes !== undefined) {
        dataToSave.notes = pendingChanges.notes;
      } else if (order?.notes) {
        // If notes weren't changed but exist in the order, include them
        dataToSave.notes = order.notes;
      }

      // Save the order data

      await apiCall("PUT", endpoints.orders.update(Number(id)), dataToSave);
      await queryClient.invalidateQueries({ queryKey: ["order", id] });
      await queryClient.invalidateQueries({ queryKey: ["orders"] });

      // Clear pending changes after successful save
      setPendingChanges({});

      showToast("Cập nhật đơn hàng thành công", "success");
    } catch (error) {
      console.error("Failed to update order:", error);
      showToast("Không thể cập nhật đơn hàng", "error");
    } finally {
      setIsSaving(false);
    }
  };

  const handleStaffSelect = useCallback(
    (staff: Staff, type: "sales" | "delivery") => {
      if (!order || !canEditOrder(order)) return;

      // Add to pending changes instead of immediate update
      updateOrder({
        [type === "sales" ? "sales_admin" : "delivery_staff"]: staff.id,
      });

      if (type === "sales") {
        setIsSalesDialogOpen(false);
      } else {
        setIsDeliveryDialogOpen(false);
      }
    },
    [
      updateOrder,
      canEditOrder,
      order,
      setIsSalesDialogOpen,
      setIsDeliveryDialogOpen,
    ]
  );

  if (isLoading || !order) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-4">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate("/orders")}
              type="text"
            >
              Trở về
            </Button>
            <h1 className="text-xl sm:text-2xl font-bold">
              Đơn hàng #{order.id}
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={selectedStatus || order.status}
              onChange={(value) => {
                if (order && user?.role === "sales_manager") {
                  // Nếu chọn trạng thái "cancelled", hiển thị modal lý do huỷ
                  if (value === "cancelled") {
                    setPendingStatus(value as Order["status"]);
                    setIsCancellationModalOpen(true);
                  } else {
                    setSelectedStatus(value as Order["status"]);
                  }
                }
              }}
              style={{ width: 150 }}
              disabled={!user || user.role !== "sales_manager"}
              options={STATUS_OPTIONS.map((option) => ({
                value: option.value,
                label: (
                  <span
                    className={`px-2 py-1 inline-block rounded w-full ${
                      statusColors[option.value as Order["status"]]
                    }`}
                  >
                    {option.label}
                  </span>
                ),
              }))}
            />
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveChanges}
              loading={isSaving}
              disabled={!canEditOrder(order)}
            >
              Lưu
            </Button>
          </div>
        </div>

        <CustomerInfoSection order={order} isMobile={isMobile} />

        <OrderItemsSection
          order={order}
          onUpdateOrder={updateOrder}
          onAddProduct={
            order && canEditOrder(order)
              ? () => setIsProductDialogOpen(true)
              : undefined
          }
          editingItem={editingItem}
          onEditItem={order && canEditOrder(order) ? setEditingItem : undefined}
          disabled={!order || !canEditOrder(order)}
          isMobile={isMobile}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <ShippingInfoSection
            order={order}
            onUpdateOrder={updateOrder}
            disabled={!order || !canEditOrder(order)}
          />
          <PaymentInfoSection
            order={order}
            onUpdateOrder={updateOrder}
            disabled={!order || !canEditOrder(order)}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <StaffAssignmentSection
            order={order}
            onSelectSalesAdmin={
              order && canEditOrder(order)
                ? () => setIsSalesDialogOpen(true)
                : undefined
            }
            onSelectDeliveryStaff={
              order && canEditOrder(order)
                ? () => setIsDeliveryDialogOpen(true)
                : undefined
            }
          />
          <NotesSection
            order={order}
            onUpdateOrder={updateOrder}
            disabled={!order || !canEditOrder(order)}
          />
        </div>
      </div>

      <ProductSelectionDialog
        isOpen={isProductDialogOpen}
        onClose={() => setIsProductDialogOpen(false)}
        onAdd={(items) => {
          if (!items.length) return;

          try {
            // Get the most up-to-date items from pendingChanges if available
            let currentItems;
            if (pendingChanges.items) {
              // Use the pending changes if available (these include any deletions)
              currentItems = JSON.parse(JSON.stringify(pendingChanges.items));
            } else {
              // Otherwise use the original order items
              currentItems = JSON.parse(JSON.stringify(order.items));
            }

            // Create a new function to merge all items properly
            const mergeAllItems = (
              existingItems: OrderItem[],
              newItems: OrderItem[]
            ) => {
              let result = [...existingItems];

              // Process each new item one by one
              for (const newItem of newItems) {
                // Find if this item already exists in the result
                const existingItemIndex = result.findIndex(
                  (i) =>
                    i.product === newItem.product &&
                    // Both null/undefined are considered the same (default variant)
                    ((i.variant === null && newItem.variant === null) ||
                      (i.variant === null && newItem.variant === undefined) ||
                      (i.variant === undefined && newItem.variant === null) ||
                      i.variant === newItem.variant)
                );

                if (existingItemIndex >= 0) {
                  // Update existing item quantity and total price
                  const existingItem = result[existingItemIndex];
                  const newQuantity = existingItem.quantity + newItem.quantity;

                  // Create a new item with updated quantity and price
                  const updatedItem = {
                    ...existingItem,
                    quantity: newQuantity,
                    total_price: Math.round(existingItem.price * newQuantity),
                  };

                  // Replace the existing item with the updated one
                  result = [
                    ...result.slice(0, existingItemIndex),
                    updatedItem,
                    ...result.slice(existingItemIndex + 1),
                  ];
                } else {
                  // Add as new item if not found
                  // Generate a unique temporary ID for new items (negative to avoid conflicts with server IDs)
                  const tempItem = {
                    ...newItem,
                    id:
                      newItem.id === 0
                        ? -Date.now() - Math.floor(Math.random() * 1000)
                        : newItem.id,
                  };
                  result.push(tempItem);
                }
              }

              return result;
            };

            // Use our new function to merge all items at once
            const updatedItems = mergeAllItems(currentItems, items);

            // Calculate the new total price
            const newTotalPrice = updatedItems.reduce(
              (sum: number, item: OrderItem) => sum + (item.total_price || 0),
              0
            );

            // Force a refresh of the order object with the new items
            const updatedOrder = {
              ...order,
              items: updatedItems,
              total_price: newTotalPrice,
            };

            // Update the query cache to immediately reflect the changes in the UI
            // Use a function to ensure we're working with the latest data
            queryClient.setQueryData(
              ["order", id],
              (oldData: Order | undefined) => {
                if (!oldData) return updatedOrder;

                return {
                  ...oldData,
                  items: updatedItems,
                  total_price: newTotalPrice,
                };
              }
            );

            // Add to pending changes
            updateOrder({
              items: updatedItems,
              total_price: newTotalPrice,
            });

            setIsProductDialogOpen(false);
          } catch (error) {
            console.error("Error adding products:", error);
            showToast("Không thể thêm sản phẩm", "error");
          }
        }}
      />

      <StaffSearchDialog
        isOpen={isSalesDialogOpen}
        onClose={() => setIsSalesDialogOpen(false)}
        onSelect={(staff) => handleStaffSelect(staff, "sales")}
        staffRole="sales_admin"
      />

      <StaffSearchDialog
        isOpen={isDeliveryDialogOpen}
        onClose={() => setIsDeliveryDialogOpen(false)}
        onSelect={(staff) => handleStaffSelect(staff, "delivery")}
        staffRole="delivery_staff"
      />

      {/* Cancellation Reason Modal */}
      <CancellationReasonModal
        isOpen={isCancellationModalOpen}
        onClose={() => {
          setIsCancellationModalOpen(false);
          setPendingStatus(null);
        }}
        onConfirm={(reason) => {
          if (!order) return;

          // Lấy ghi chú hiện tại
          const existingNotes = order.notes || "";
          const cancellationNote = `Lý do huỷ: ${reason}`;

          // Thêm lý do huỷ vào ghi chú hiện tại
          const updatedNotes = existingNotes
            ? `${existingNotes}\n${cancellationNote}`
            : cancellationNote;

          // Cập nhật trạng thái và ghi chú
          setSelectedStatus("cancelled");
          updateOrder({ notes: updatedNotes });

          // Đóng modal
          setIsCancellationModalOpen(false);
          setPendingStatus(null);
        }}
        title="Lý do huỷ đơn hàng"
      />
    </>
  );
}
