// src/pages/OrderImportPage.tsx
import React from "react";
import { useOrderImport } from "@/hooks/useOrderImport";
import { OrderImportInstructions } from "@/components/orders/Import/OrderImportInstructions";
import { OrderImportActions } from "@/components/orders/Import/OrderImportActions";
import { OrderImportTable } from "@/components/orders/Import/OrderImportTable";

const OrderImportPage = () => {
  const {
    data,
    isSaving,
    processedRows,
    hasErrors,
    handleFileChange,
    handleSaveOrders,
    clearData,
    downloadErrorRows,
  } = useOrderImport();

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-4">Import Đơn Hàng</h1>

      <OrderImportInstructions />

      <OrderImportActions
        onFileChange={handleFileChange}
        onClearData={clearData}
        onSaveData={handleSaveOrders}
        isSaving={isSaving}
        hasData={data.length > 0}
      />

      <OrderImportTable
        data={data}
        processedRows={processedRows}
        hasErrors={hasErrors}
        onDownloadErrors={downloadErrorRows}
      />
    </div>
  );
};

export default OrderImportPage;
