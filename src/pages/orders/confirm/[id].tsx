import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useAuth } from "../../../context/auth-hooks";
import { useToast } from "../../../context/toast-hooks";
import { Button } from "../../../components/ui/button";
import { apiCall, endpoints } from "../../../lib/api";
import { CancellationReasonModal } from "../../../components/orders/CancellationReasonModal";
import { Order } from "../../../types/order";
import axios from "axios";

export default function OrderConfirmationPage() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isCancellationModalOpen, setIsCancellationModalOpen] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const canUpdateOrderStatus =
    user?.role === "sales_manager" ||
    user?.role === "warehouse_staff" ||
    user?.role === "delivery_staff";

  // Hàm xử lý khi xác nhận lý do huỷ
  const handleCancellationConfirm = async (reason: string) => {
    if (!user || !id) return;

    setIsLoading(true);
    try {
      // Lấy thông tin đơn hàng hiện tại để lấy notes
      const orderData = await apiCall<Order>(
        "GET",
        endpoints.orders.detail(Number(id))
      );

      // Chuẩn bị ghi chú mới với lý do huỷ
      const existingNotes = orderData.notes || "";
      const cancellationNote = `Lý do huỷ: ${reason}`;
      const updatedNotes = existingNotes
        ? `${existingNotes}\n${cancellationNote}`
        : cancellationNote;

      // Cập nhật đơn hàng với trạng thái huỷ và ghi chú mới
      await apiCall("PUT", endpoints.orders.update(Number(id)), {
        status: "cancelled",
        notes: updatedNotes,
      });

      setIsSuccess(true);
      showToast("Huỷ đơn hàng thành công", "success");
    } catch (error: unknown) {
      console.error("Failed to cancel order:", error);
      if (axios.isAxiosError(error)) {
        const message =
          error.response?.data?.detail || "Không thể huỷ đơn hàng";
        showToast(message, "error");
      } else {
        showToast("Đã xảy ra lỗi khi huỷ đơn hàng", "error");
      }
    } finally {
      setIsLoading(false);
      setIsCancellationModalOpen(false);
    }
  };

  useEffect(() => {
    const confirmOrder = async () => {
      if (!user || !id) return;

      if (!canUpdateOrderStatus) {
        showToast("Bạn không có quyền xác nhận đơn hàng", "error");
        return;
      }

      const status = searchParams.get("status");
      const deliveryType = searchParams.get("delivery_type");
      const deliveryStaffId = searchParams.get("delivery_staff_id");

      // Nếu là yêu cầu huỷ đơn hàng, hiển thị modal lý do huỷ
      if (status === "cancelled") {
        setIsCancellationModalOpen(true);
        return;
      }

      setIsLoading(true);
      try {
        // Special case: Warehouse staff can update to delivered with specific delivery types
        const isWarehouseDeliveryException =
          user.role === "warehouse_staff" &&
          status === "delivered" &&
          (deliveryType === "grab" || deliveryType === "company_vehicle");

        // Allow all roles to cancel orders
        const isCancellationRequest = status === "cancelled";

        // Validate role and status combinations
        if (
          user.role === "warehouse_staff" &&
          status !== "shipped" &&
          !isWarehouseDeliveryException &&
          !isCancellationRequest
        ) {
          showToast(
            "Nhân viên kho chỉ có thể cập nhật trạng thái đã giao cho vận chuyển",
            "error"
          );
          return;
        }

        if (
          user.role === "delivery_staff" &&
          status !== "delivered" &&
          !isCancellationRequest
        ) {
          showToast(
            "Nhân viên giao hàng chỉ có thể cập nhật trạng thái đã giao hàng",
            "error"
          );
          return;
        }

        // Ensure status is not null
        if (!status) {
          showToast("Trạng thái không hợp lệ", "error");
          return;
        }

        // Prepare payload for API call
        const payload: {
          status: string;
          delivery_staff_id?: number;
          shipping_unit?: string;
        } = {
          status: status,
        };

        // Add delivery_staff to payload if deliveryStaffId is present
        if (deliveryStaffId) {
          payload.delivery_staff_id = Number(deliveryStaffId);
        }

        if (deliveryType) {
          payload.shipping_unit = deliveryType;
        }

        // Call API with payload
        await apiCall(
          "POST",
          endpoints.orders.updateStatus(Number(id)),
          payload
        );
        setIsSuccess(true);
        showToast("Cập nhật trạng thái đơn hàng thành công", "success");
      } catch (error: unknown) {
        console.error("Failed to confirm order:", error);
        if (axios.isAxiosError(error)) {
          const message =
            error.response?.data?.detail || "Không thể xác nhận đơn hàng";
          showToast(message, "error");
        } else {
          showToast("Đã xảy ra lỗi khi xác nhận đơn hàng", "error");
        }
      } finally {
        setIsLoading(false);
      }
    };

    confirmOrder();
  }, [id, user, searchParams]);

  const handleBackToOrders = () => {
    navigate("/orders");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <p className="text-muted-foreground">
            Đang cập nhật trạng thái đơn hàng...
          </p>
        </div>
      </div>
    );
  }

  if (isSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary">
            Đơn hàng #{id} đã được cập nhật
          </h1>
          <p className="mt-4 text-muted-foreground">
            Đơn hàng đã được cập nhật trạng thái thành công
          </p>
          <Button onClick={handleBackToOrders} className="mt-6">
            Quay lại danh sách đơn hàng
          </Button>
        </div>
      </div>
    );
  }
  if (!isSuccess) {
    return (
      <>
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-destructive">
              Không thể xác nhận đơn hàng
            </h1>
            <p className="mt-4 text-muted-foreground">
              Đã xảy ra lỗi khi xác nhận đơn hàng. Vui lòng thử lại sau.
            </p>
            <Button onClick={handleBackToOrders} className="mt-6">
              Quay lại danh sách đơn hàng
            </Button>
          </div>
        </div>

        {/* Modal lý do huỷ đơn hàng */}
        <CancellationReasonModal
          isOpen={isCancellationModalOpen}
          onClose={() => {
            setIsCancellationModalOpen(false);
            navigate("/orders");
          }}
          onConfirm={handleCancellationConfirm}
          title="Lý do huỷ đơn hàng"
        />
      </>
    );
  }
}
