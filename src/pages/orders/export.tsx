import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input, Table, Spin, Pagination } from 'antd';
import FieldSelectionModal, { availableExportFields } from '@/components/orders/Export/FieldSelectionModal';
import type { ColumnsType } from 'antd/es/table';
import { ArrowLeft, Search, FileDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { apiCall, endpoints } from '@/lib/api';
import { Order, OrderItem, PAYMENT_STATUS_OPTIONS, STATUS_OPTIONS } from '@/types/order'; // Keep STATUS_OPTIONS for table filters
import { formatCurrency } from '@/lib/utils';
import { useAuth } from '@/context/auth-hooks';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import { useToast } from '@/context/toast-hooks';
import { DateRangePickerWithPresets } from '@/components/common/DateRangePickerWithPresets';
import {
  DEFAULT_EXPORT_FIELDS,
  EXPORT_BUTTON_TEXTS,
  EXPORT_PAGE_TITLES,
  EXPORT_PLACEHOLDERS,
  EXPORT_TOAST_MESSAGES,
  formatDateForOrderDisplay,
  getOrderPaymentStatusLabel,
  getOrderStatusLabel,
  ITEMS_PER_PAGE,
  EXCEL_EXPORT,
  SINGLE_ORDER_EXPORT_ITEM_COLUMNS,
  SINGLE_ORDER_EXPORT_SUMMARY_LABELS,
} from '@/utils/constants';
import { NAVIGATION_PATHS } from '@/constants/navigationPaths';

const OrderExportPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<[string | null, string | null]>([null, null]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectAllFilteredActive, setSelectAllFilteredActive] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = ITEMS_PER_PAGE;
  const [isExportingMulti, setIsExportingMulti] = useState(false);

  const [isFieldSelectionModalVisible, setIsFieldSelectionModalVisible] = useState(false);
  const [selectedExportFields, setSelectedExportFields] = useState<string[]>(DEFAULT_EXPORT_FIELDS);

  useEffect(() => {
    if (selectedExportFields.length === 0 && DEFAULT_EXPORT_FIELDS.length > 0) {
       setSelectedExportFields(DEFAULT_EXPORT_FIELDS);
    }
  }, [selectedExportFields]); // Or [] if only on mount.

  const { data: ordersData, isLoading, error } = useQuery<{ results: Order[]; count: number }>({
    queryKey: ['ordersForExport', currentPage, searchTerm, dateRange, user?.id, user?.role],
    queryFn: async () => {
      let url = `${endpoints.orders.list}?page=${currentPage}&page_size=${pageSize}`;
      if (searchTerm) {
        // Assuming search by order ID or customer name for simplicity
        // Adjust search_by parameter as needed by your API
        url += `&search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id`;
      }

      // Add date range parameters
      if (dateRange[0]) {
        url += `&date_from=${dateRange[0]}`;
      }
      if (dateRange[1]) {
        url += `&date_to=${dateRange[1]}`;
      }

      // Apply role-based filtering if necessary, similar to useTableOrders
      if (user?.role === "sales_admin" && user.id) {
         url = `${endpoints.orders.list}/by_sales_admin/?id=${user.id}&page=${currentPage}&page_size=${pageSize}`;
         if (searchTerm) {
           url += `&search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id`;
         }
         // Add date range parameters for sales_admin
         if (dateRange[0]) {
           url += `&date_from=${dateRange[0]}`;
         }
         if (dateRange[1]) {
           url += `&date_to=${dateRange[1]}`;
         }
      }
      return apiCall('GET', url);
    },
    enabled: !!user, // Only run query if user is loaded
  });

  const orders = useMemo(() => ordersData?.results || [], [ordersData]);
  const totalOrders = useMemo(() => ordersData?.count || 0, [ordersData]);

  // Function to fetch all orders matching the current filters
  const fetchAllFilteredOrders = async (): Promise<Order[]> => {
    if (!user) return [];

    let allMatchingOrders: Order[] = [];
    let currentPageToFetch = 1;
    const fetchPageSize = 100; // Fetch in chunks of 100, or adjust as needed
    let hasMore = true;
    let totalFetchedCount = 0; // To avoid infinite loops if API count is off

    // Construct base URL similar to useQuery
    let baseUrl = `${endpoints.orders.list}?`;
    if (user.role === "sales_admin" && user.id) {
      baseUrl = `${endpoints.orders.list}/by_sales_admin/?id=${user.id}&`;
    }
    if (searchTerm) {
      baseUrl += `search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id&`;
    }

    // Add date range parameters
    if (dateRange[0]) {
      baseUrl += `date_from=${dateRange[0]}&`;
    }
    if (dateRange[1]) {
      baseUrl += `date_to=${dateRange[1]}&`;
    }

    showToast(EXPORT_TOAST_MESSAGES.LOADING_ALL_ORDERS, "success");

    while (hasMore) {
      const url = `${baseUrl}page=${currentPageToFetch}&page_size=${fetchPageSize}`;
      try {
        const response = await apiCall<{ results: Order[]; count?: number }>('GET', url);
        if (response.results && response.results.length > 0) {
          allMatchingOrders = allMatchingOrders.concat(response.results);
          totalFetchedCount += response.results.length;
        }
        if (response.results && response.results.length > 0) {
          currentPageToFetch++;

          // Safety break:
          // if (currentPageToFetch > 50) {
          //   showToast(EXPORT_TOAST_MESSAGES.FETCH_LIMIT_REACHED, "error");
          //   hasMore = false;
          // }
        } else {
          hasMore = false;
        }
      } catch (fetchError: any) {
        if (fetchError?.response?.status === 404) {
          hasMore = false;
        } else {
          console.error("Error fetching all filtered orders:", fetchError);
          showToast(EXPORT_TOAST_MESSAGES.FETCH_ALL_ORDERS_ERROR, "error");
          hasMore = false;
          return [];
        }
      }
    }
    if (allMatchingOrders.length > 0) {
        showToast(EXPORT_TOAST_MESSAGES.ORDERS_LOADED(allMatchingOrders.length), "success");
    } else {
        showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_FOUND_FILTER, "success");
    }
    return allMatchingOrders;
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Reset to first page when date range changes
  const handleDateRangeChange = (dates: [string | null | undefined, string | null | undefined]) => {
    setDateRange([
      dates[0] || null,
      dates[1] || null
    ]);
    setCurrentPage(1); // Reset to first page on date range change
  };

  // Handler for when individual row selections change OR table header checkbox is used
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectAllFilteredActive(false); // Any individual selection change deactivates "select all filtered" mode
  };

  const rowSelection = {
    // If selectAllFilteredActive is true, all items on the current page should appear selected
    selectedRowKeys: selectAllFilteredActive ? orders.map(o => o.id) : selectedRowKeys,
    onChange: onSelectChange,
  };

  // Renamed from handleSelectAll, now specifically for "select all filtered"
  const handleToggleSelectAllFiltered = () => {
    if (selectAllFilteredActive) {
      setSelectedRowKeys([]);
      setSelectAllFilteredActive(false);
    } else {
      setSelectedRowKeys(orders.map(order => order.id)); // Select current page for visual cue
      setSelectAllFilteredActive(true);
    }
  };



  const handleProceedWithMultiOrderExport = async () => {
    if (selectedExportFields.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.SELECT_AT_LEAST_ONE_FIELD, "error");
      return;
    }
    setIsExportingMulti(true);
    showToast(EXPORT_TOAST_MESSAGES.PREPARING_EXCEL, "success");

    let ordersToExportInTable: Order[] = [];
    if (selectAllFilteredActive) {
      // Fetch all orders that match the current filter criteria
      ordersToExportInTable = await fetchAllFilteredOrders();
    } else {
      // Export only the selected orders from the current page
      ordersToExportInTable = orders.filter(order => selectedRowKeys.includes(order.id));
    }

    if (ordersToExportInTable.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_SELECTED_FOR_EXPORT, "error");
      setIsExportingMulti(false);
      setIsFieldSelectionModalVisible(false);
      return;
    }

    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet(EXCEL_EXPORT.MULTI_ORDER_SHEET_NAME);

    // Add Headers
    const headers = selectedExportFields.map(fieldValue => {
      const field = availableExportFields.find(f => f.value === fieldValue);  // This now uses the imported availableExportFields
      return field ? field.label : fieldValue;
    });
    worksheet.addRow(headers).font = { bold: true };

    console.log(ordersToExportInTable)

    // Add Data Rows
    ordersToExportInTable.forEach(order => {
      const rowData = selectedExportFields.map(fieldValue => {
        switch (fieldValue) {
          case 'id':
            return order.id;
          case 'created_at':
            return formatDateForOrderDisplay(order.created_at);
          case 'customer_name':
            return order.user?.full_name || 'N/A';
          case 'phone_number':
            return order.phone_number;
          case 'shipping_address':
            return `${order.shipping_address}, Phường ${order.ward}, Quận ${order.district}, Thành phố ${order.city}`;
          case 'items_summary':
            return order.items.map(item => `${item.product_name}${item.variant_name ? ` (${item.variant_name})` : ''} x${item.quantity}`).join('; ');
          case 'payment_method':
            return order.payment_method; // Assuming payment_method is a string label
          case 'final_total':
            return order.final_total;
          case 'status':
            return getOrderStatusLabel(order.status);
          case 'payment_status':
            return getOrderPaymentStatusLabel(order.payment_status);
          case 'notes':
            return order.notes;
          default:
            return '';
        }
      });
      worksheet.addRow(rowData);
    });

    // Style currency columns if selected
    selectedExportFields.forEach((fieldValue, index) => {
        if (fieldValue === 'final_total') {
            worksheet.getColumn(index + 1).numFmt = '#,##0 "₫"';
            worksheet.getColumn(index + 1).alignment = { horizontal: 'right' };
        }
    });

    // Auto-fit columns
     worksheet.columns.forEach(column => {
        let maxLength = 0;
        column.eachCell!({ includeEmpty: true }, cell => {
            const columnLength = cell.value ? cell.value.toString().length : 10;
            if (columnLength > maxLength) {
                maxLength = columnLength;
            }
        });
        column.width = maxLength < 10 ? 10 : Math.min(maxLength + 2, 50); // Max width 50
    });


    try {
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
      const fileName = `${EXCEL_EXPORT.MULTI_ORDER_FILE_NAME_PREFIX}${new Date().toISOString().slice(0,10)}.xlsx`;
      saveAs(blob, fileName);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_SUCCESS, "success");
    } catch (err) {
      console.error('Lỗi khi tạo file XLSX cho nhiều đơn hàng:', err);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_ERROR, "error");
    } finally {
      setIsExportingMulti(false);
      setIsFieldSelectionModalVisible(false);
    }
  };

  const handleExportSelected = async () => {
    if (selectAllFilteredActive) {
      // TODO: Implement actual XLSX export logic for ALL filtered orders.
      // This will require fetching all pages of data matching `searchTerm` and other filters.
      if (totalOrders > 0) {
        setIsFieldSelectionModalVisible(true);
      } else {
        showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_MATCH_FILTER_TO_EXPORT, "error");
      }
    } else {
      if (selectedRowKeys.length === 0) {
        showToast(EXPORT_TOAST_MESSAGES.SELECT_AT_LEAST_ONE_ORDER, "error");
        return;
      }

      if (selectedRowKeys.length === 1) {
        const orderIdToExport = selectedRowKeys[0];
        const orderToExport = orders.find(order => order.id === orderIdToExport);

        if (!orderToExport) {
          showToast(EXPORT_TOAST_MESSAGES.SELECTED_ORDER_NOT_FOUND, "error");
          return;
        }

        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet(`${EXCEL_EXPORT.SINGLE_ORDER_SHEET_NAME_PREFIX}${orderToExport.id}`);

        // Using direct strings since we don't have header labels defined in constants
        worksheet.addRow([`Mã Đơn Hàng: ${orderToExport.id}`]);
        worksheet.addRow([`Khách hàng: ${orderToExport.user?.full_name || 'N/A'}`]);
        worksheet.addRow([`Ngày tạo: ${formatDateForOrderDisplay(orderToExport.created_at)}`]);
        worksheet.addRow([]);

        worksheet.addRow(SINGLE_ORDER_EXPORT_ITEM_COLUMNS);
        worksheet.getRow(worksheet.lastRow!.number).font = { bold: true };


        // Item Table Data
        orderToExport.items.forEach((item: OrderItem) => {
          worksheet.addRow([
            item.variant_name ? `${item.product_name} (${item.variant_name})` : item.product_name,
            item.quantity,
            item.price, // Assuming price is unit price
            item.total_price,
          ]);
        });

        // Style numeric columns (optional)
        worksheet.getColumn('B').alignment = { horizontal: 'right' };
        worksheet.getColumn('C').numFmt = EXCEL_EXPORT.CURRENCY_FORMAT;
        worksheet.getColumn('C').alignment = { horizontal: 'right' };
        worksheet.getColumn('D').numFmt = EXCEL_EXPORT.CURRENCY_FORMAT;
        worksheet.getColumn('D').alignment = { horizontal: 'right' };


        // Empty row for spacing
        worksheet.addRow([]);

        // Summary Section
        // Ensure all item total_prices are numbers
        const subtotal = orderToExport.items.reduce((sum, item) => sum + Number(item.total_price || 0), 0);

        const shippingFee = Number(orderToExport.shipping_fee || 0);
        const discount = Number(orderToExport.discount || 0);
        const taxRate = Number(orderToExport.tax || 0); // This is a rate like 0.1 for 10%

        worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.SUBTOTAL, '', '', subtotal]);
        worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.SHIPPING_FEE, '', '', shippingFee]);
        worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.DISCOUNT, '', '', discount]);
        worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.TAX_PERCENTAGE, '', '', taxRate * 100]);
        const totalBeforeTax = subtotal + shippingFee - discount;
        const calculatedFinalTotal = Math.round(totalBeforeTax * (1 + taxRate));

        worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.TOTAL, '', '', calculatedFinalTotal]).font = { bold: true };

        // Style summary values
        const summaryStartRow = worksheet.lastRow!.number - 4; // Tạm tính is the first of these 5 rows
        for (let i = 0; i < 5; i++) { // Iterate 5 times for Tạm tính, Phí GH, Giảm giá, Thuế, Tổng cộng
            const labelCell = worksheet.getCell(`A${summaryStartRow + i}`);
            const valueCell = worksheet.getCell(`D${summaryStartRow + i}`);

            valueCell.numFmt = EXCEL_EXPORT.CURRENCY_FORMAT;
            valueCell.alignment = { horizontal: 'right' };

            if (i === 3) { // Tax percentage row
                valueCell.numFmt = EXCEL_EXPORT.PERCENTAGE_FORMAT;
                valueCell.numFmt = EXCEL_EXPORT.NUMBER_FORMAT;
            }

            if (i === 4) { // Total row
                 labelCell.font = { bold: true };
                 valueCell.font = { bold: true };
            }
        }
        // Re-adjusting tax row to show percentage value correctly
        worksheet.getCell(`D${summaryStartRow + 3}`).numFmt = EXCEL_EXPORT.NUMBER_FORMAT;
        worksheet.getCell(`D${summaryStartRow + 3}`).value = taxRate * 100;

        // Apply currency format to all monetary values
        worksheet.getCell(`D${summaryStartRow + 0}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Subtotal
        worksheet.getCell(`D${summaryStartRow + 1}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Shipping fee
        worksheet.getCell(`D${summaryStartRow + 2}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Discount
        worksheet.getCell(`D${summaryStartRow + 4}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Total


        // Auto-fit columns (optional, can be resource-intensive for large sheets)
        worksheet.columns.forEach(column => {
            let maxLength = 0;
            column.eachCell!({ includeEmpty: true }, cell => {
                let columnLength = cell.value ? cell.value.toString().length : 10;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }
            });
            column.width = maxLength < 10 ? 10 : maxLength + 2;
        });

        // Generate and Download XLSX
        try {
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
            const fileName = `${EXCEL_EXPORT.SINGLE_ORDER_FILE_NAME_PREFIX}${orderToExport.id}.xlsx`;
            saveAs(blob, fileName);
            showToast(EXPORT_TOAST_MESSAGES.SINGLE_ORDER_EXPORT_SUCCESS, "success");
        } catch (err) {
            console.error('Lỗi khi tạo file XLSX:', err);
            showToast(EXPORT_TOAST_MESSAGES.EXPORT_ERROR, "error");
        }

      } else { // selectedRowKeys.length > 1
        setIsFieldSelectionModalVisible(true); // Show modal for field selection
      }
    }
  };

  const columns: ColumnsType<Order> = [
    {
      title: 'Mã ĐH',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => formatDateForOrderDisplay(text),
      width: 120,
      sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    },
    {
      title: 'Khách hàng',
      key: 'customer',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.user?.full_name || 'N/A'}</div> {/* Use optional chaining and fallback for user */}
          <div className="text-sm text-muted-foreground">{record.phone_number}</div>
        </div>
      ),
      sorter: (a, b) => (a.user?.full_name || '').localeCompare(b.user?.full_name || ''),
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'final_total',
      key: 'final_total',
      render: (text: number) => formatCurrency(text),
      width: 150,
      sorter: (a, b) => a.final_total - b.final_total,
    },
    {
      title: 'Trạng thái ĐH',
      dataIndex: 'status',
      key: 'status',
      render: (status: Order['status']) => getOrderStatusLabel(status), // Use Order['status']
      width: 150,
      filters: STATUS_OPTIONS.map(s => ({text: s.label, value: s.value})),
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Trạng thái TT',
      dataIndex: 'payment_status',
      key: 'payment_status',
      render: (status: Order['payment_status']) => getOrderPaymentStatusLabel(status),
      width: 150,
      filters: PAYMENT_STATUS_OPTIONS.map(s => ({text: s.label, value: s.value})),
      onFilter: (value, record) => record.payment_status === value,
    }
  ];

  if (error) {
    return (
      <div className="container mx-auto py-6 text-red-500">
        Lỗi khi tải danh sách đơn hàng: {error.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-4 flex items-center justify-between">
        <Button variant="outline" onClick={() => navigate(NAVIGATION_PATHS.ORDERS)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {EXPORT_BUTTON_TEXTS.BACK_TO_ORDERS}
        </Button>
        <h1 className="text-2xl font-bold">{EXPORT_PAGE_TITLES.EXPORT_ORDERS_PAGE}</h1>
      </div>

      <div className="mb-4 p-4 border rounded-md bg-background shadow">
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <Input
            placeholder={EXPORT_PLACEHOLDERS.SEARCH_ORDERS}
            prefix={<Search className="h-4 w-4 text-muted-foreground" />}
            value={searchTerm}
            onChange={handleSearch}
            className="flex-grow"
          />
          <div className="min-w-[300px]">
            <DateRangePickerWithPresets
              value={dateRange}
              onChange={handleDateRangeChange}
              className="w-full"
            />
          </div>
        </div>
        <div className="flex flex-wrap gap-2 mb-4 items-center">
          <Button
            onClick={handleToggleSelectAllFiltered}
            disabled={isLoading || totalOrders === 0}
            variant={selectAllFilteredActive ? "destructive" : "outline"}
          >
            {selectAllFilteredActive
              ? EXPORT_BUTTON_TEXTS.DESELECT_ALL_FILTERED(totalOrders)
              : EXPORT_BUTTON_TEXTS.SELECT_ALL_FILTERED(totalOrders)}
          </Button>
          <Button
            onClick={handleExportSelected}
            disabled={isLoading || (selectedRowKeys.length === 0 && !selectAllFilteredActive)}
            variant="default"
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <FileDown className="mr-2 h-4 w-4" />
            {selectAllFilteredActive
              ? EXPORT_BUTTON_TEXTS.EXPORT_ALL_FILTERED(totalOrders)
              : EXPORT_BUTTON_TEXTS.EXPORT_SELECTED(selectedRowKeys.length)}
          </Button>
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      )}

      {!isLoading && orders.length === 0 && searchTerm && (
         <div className="text-center py-8 text-muted-foreground">
           {EXPORT_TOAST_MESSAGES.NO_ORDERS_MATCH_SEARCH(searchTerm)}
         </div>
      )}
       {!isLoading && orders.length === 0 && !searchTerm && (
         <div className="text-center py-8 text-muted-foreground">
           {EXPORT_TOAST_MESSAGES.NO_ORDERS_TO_DISPLAY}
         </div>
      )}

      {!isLoading && orders.length > 0 && (
        <>
          <Table
            rowKey="id"
            rowSelection={rowSelection}
            columns={columns}
            dataSource={orders}
            pagination={false} // Using custom pagination
            scroll={{ x: 'max-content' }}
            className="bg-background shadow rounded-md"
          />
          {totalOrders > pageSize && (
            <div className="flex justify-center mt-4">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalOrders}
                onChange={(page) => setCurrentPage(page)}
                showSizeChanger={false}
              />
            </div>
          )}
        </>
      )}
      <FieldSelectionModal
        visible={isFieldSelectionModalVisible}
        onOk={handleProceedWithMultiOrderExport}
        onCancel={() => setIsFieldSelectionModalVisible(false)}
        selectedFields={selectedExportFields}
        onFieldsChange={setSelectedExportFields}
        isExporting={isExportingMulti}
      />
    </div>
  );
};

export default OrderExportPage;
