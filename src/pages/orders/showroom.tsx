import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, Pagin<PERSON> } from "antd";
import { OrderSearch } from "../../components/orders/OrderSearch";
import { OrderStatusTabs } from "../../components/orders/OrderStatusTabs";
import { OrderTable } from "../../components/orders/OrderTable";
import { OrderFilterControl } from "../../components/orders/OrderFilterControl";
import { StaffFilter } from "../../components/orders/StaffFilter";
import { useTableOrders } from "../../hooks/useTableOrders";
import { useAuth } from "../../context/auth-hooks";

export default function ShowroomOrdersPage() {
  const navigate = useNavigate();
  const { user } = useAuth();

  const {
    page,
    statusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedDeliveryStaff,
    selectedShippingUnit,
    paymentStatusFilter,
    orders,
    totalCount,
    statusCounts,
    staffList,
    isLoading,
    setPage,
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleDeliveryStaffChange,
    handleShippingUnitChange,
    handlePaymentStatusFilterChange,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
    updatePaymentStatus,
  } = useTableOrders({
    userId: user?.id,
    userRole: user?.role,
    showroomOnly: true
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Đơn hàng Showroom</h1>
        {(user?.role === "sales_admin" || user?.role === "sales_manager") && (
          <Button
            type="primary"
            onClick={() => navigate("/orders/create")}
            size="large"
          >
            Tạo đơn hàng
          </Button>
        )}
      </div>

      <OrderSearch onSearch={handleSearch} initialParams={searchParams} />

      <StaffFilter
        userRole={user?.role || ""}
        salesAdmin={selectedSalesAdmin}
        deliveryStaff={selectedDeliveryStaff}
        shippingUnit={selectedShippingUnit}
        onSalesAdminChange={handleSalesAdminChange}
        onDeliveryStaffChange={handleDeliveryStaffChange}
        onShippingUnitChange={handleShippingUnitChange}
        staffList={staffList}
      />

      <OrderStatusTabs
        currentStatus={statusFilter}
        onChange={handleStatusChange}
        counts={statusCounts}
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          <OrderTable
            orders={orders}
            staffList={staffList}
            onUpdateStatus={updateOrderStatus}
            onUpdateDeliveryMethod={updateDeliveryMethod}
            onUpdatePaymentMethod={updatePaymentMethod}
            onUpdatePaymentStatus={updatePaymentStatus}
          />

          {orders.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              Không tìm thấy đơn hàng nào phù hợp.
            </div>
          )}

          {orders.length > 0 && (
            <div className="flex justify-center mt-4">
              <Pagination
                current={page}
                total={totalCount}
                pageSize={10}
                onChange={(page) => setPage(page)}
                showSizeChanger={false}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
