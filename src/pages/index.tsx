import { useState, useEffect } from "react";
import dayjs from "dayjs";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { Tabs } from "antd";
import { DateRangePickerWithPresets } from "@/components/common/DateRangePickerWithPresets";
import { KpiCards } from "@/components/dashboard/KpiCards";
import { RevenueChart } from "@/components/dashboard/RevenueChart";
import { StaffFilter } from "@/components/orders/StaffFilter";
import { apiCall, endpoints } from "@/lib/api";
import { Staff } from "@/types/staff";
import { DashboardStats } from "@/types/report";
import { useAuth } from "@/context/auth-hooks";

export default function DashboardPage() {
  const { user } = useAuth();
  const [urlSearchParams, setUrlSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<"actual" | "all">("actual");

  // Initialize states from URL params
  const [dateRange, setDateRange] = useState<[string | null, string | null]>(
    () => {
      const dateFrom = urlSearchParams.get("dateFrom");
      const dateTo = urlSearchParams.get("dateTo");
      return dateFrom && dateTo
        ? [dateFrom, dateTo]
        : [
            dayjs().subtract(30, "day").format("YYYY-MM-DD"),
            dayjs().format("YYYY-MM-DD"),
          ];
    }
  );

  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<Staff | null>(
    () => {
      // If user is sales_admin, use their ID as default
      if (user?.role === "sales_admin") {
        return { id: user.id } as Staff;
      }
      // Otherwise check URL params
      const id = urlSearchParams.get("sales_admin");
      return id ? ({ id: Number(id) } as Staff) : null;
    }
  );

  const [selectedDeliveryStaff, setSelectedDeliveryStaff] =
    useState<Staff | null>(() => {
      const id = urlSearchParams.get("delivery_staff");
      return id ? ({ id: Number(id) } as Staff) : null;
    });

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    if (dateRange[0]) {
      params.set("dateFrom", dateRange[0]);
    }
    if (dateRange[1]) {
      params.set("dateTo", dateRange[1]);
    }
    if (selectedSalesAdmin) {
      params.set("sales_admin", String(selectedSalesAdmin.id));
    }
    if (selectedDeliveryStaff) {
      params.set("delivery_staff", String(selectedDeliveryStaff.id));
    }

    setUrlSearchParams(params, { replace: true });
  }, [
    dateRange,
    selectedSalesAdmin,
    selectedDeliveryStaff,
    setUrlSearchParams,
  ]);

  // Fetch staff list
  const { data: staffList = [] } = useQuery<Staff[]>({
    queryKey: ["staff"],
    queryFn: async () => {
      const response = await apiCall<{ results: Staff[] }>(
        "GET",
        endpoints.customers.list + "?is_staff=true&no_page=true"
      );

      return response.results;
    },
  });

  // Fetch actual dashboard stats (excluding cancelled orders)
  const { data: actualDashboardData, isLoading: isActualLoading } =
    useQuery<DashboardStats>({
      queryKey: [
        "dashboard-actual",
        dateRange[0],
        dateRange[1],
        selectedSalesAdmin?.id,
        selectedDeliveryStaff?.id,
      ],
      queryFn: async () => {
        if (!dateRange[0] || !dateRange[1])
          return {
            total_revenue: 0,
            total_orders: 0,
            average_order_value: 0,
            pending_orders: 0,
            revenue_by_date: [],
            orders_by_status: {},
          };

        const params = new URLSearchParams();

        params.set("dateFrom", dateRange[0]);
        params.set("dateTo", dateRange[1]);

        if (selectedSalesAdmin?.id) {
          params.set("sales_admin", String(selectedSalesAdmin.id));
        }
        if (selectedDeliveryStaff?.id) {
          params.set("delivery_staff", String(selectedDeliveryStaff.id));
        }

        const query = params.toString();
        const url = `${endpoints.reports.list}?${query}`;
        return apiCall("GET", url);
      },
    });

  // Fetch all dashboard stats (including cancelled orders)
  const { data: allDashboardData, isLoading: isAllLoading } =
    useQuery<DashboardStats>({
      queryKey: [
        "dashboard-all",
        dateRange[0],
        dateRange[1],
        selectedSalesAdmin?.id,
        selectedDeliveryStaff?.id,
      ],
      queryFn: async () => {
        if (!dateRange[0] || !dateRange[1])
          return {
            total_revenue: 0,
            total_orders: 0,
            average_order_value: 0,
            pending_orders: 0,
            revenue_by_date: [],
            orders_by_status: {},
          };

        const params = new URLSearchParams();

        params.set("dateFrom", dateRange[0]);
        params.set("dateTo", dateRange[1]);

        if (selectedSalesAdmin?.id) {
          params.set("sales_admin", String(selectedSalesAdmin.id));
        }
        if (selectedDeliveryStaff?.id) {
          params.set("delivery_staff", String(selectedDeliveryStaff.id));
        }

        const query = params.toString();
        const url = `${endpoints.reports.listAll}?${query}`;
        return apiCall("GET", url);
      },
    });

  const handleSalesAdminChange = (staffId: number | null) => {
    setSelectedSalesAdmin(
      staffId ? staffList.find((s) => s.id === staffId) || null : null
    );
  };

  const handleDeliveryStaffChange = (staffId: number | null) => {
    setSelectedDeliveryStaff(
      staffId ? staffList.find((s) => s.id === staffId) || null : null
    );
  };

  // Get the current dashboard data based on active tab
  const dashboardData =
    activeTab === "actual" ? actualDashboardData : allDashboardData;
  const isLoading = activeTab === "actual" ? isActualLoading : isAllLoading;

  // Transform status data for charts
  const revenueData =
    dashboardData?.revenue_by_date.map(
      (item: { date: string; total_revenue: number }) => ({
        date: item.date,
        revenue: item.total_revenue,
      })
    ) || [];

  // const orderStatusData = dashboardData
  //   ? Object.entries(dashboardData.orders_by_status).map(([status, count]) => ({
  //       status,
  //       count,
  //     }))
  //   : [];

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <DateRangePickerWithPresets
            value={dateRange}
            onChange={(dates) =>
              setDateRange([dates[0] || null, dates[1] || null])
            }
          />
        </div>

        <StaffFilter
          userRole={user?.role || ""}
          salesAdmin={selectedSalesAdmin}
          deliveryStaff={selectedDeliveryStaff}
          onSalesAdminChange={handleSalesAdminChange}
          onDeliveryStaffChange={handleDeliveryStaffChange}
          staffList={staffList}
        />

        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as "actual" | "all")}
          items={[
            {
              key: "actual",
              label: "Doanh thu thực tế",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị doanh thu thực tế
                </div>
              ),
            },
            {
              key: "all",
              label: "Doanh thu tổng",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị tổng doanh thu (bao gồm cả đơn hàng đã hủy và đang xử
                  lí)
                </div>
              ),
            },
          ]}
        />
      </div>

      <div className="relative">
        {activeTab === "all" && (
          <div className="absolute right-0 top-0 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full z-10">
            Doanh thu tổng
          </div>
        )}
        <KpiCards
          totalRevenue={dashboardData?.total_revenue ?? 0}
          totalOrders={dashboardData?.total_orders ?? 0}
          avgOrderValue={dashboardData?.average_order_value ?? 0}
          processingOrders={dashboardData?.pending_orders ?? 0}
          isLoading={isLoading}
        />
      </div>

      <div className="grid gap-6 grid-cols-1">
        <div className="relative">
          {activeTab === "all" && (
            <div className="absolute right-0 top-0 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full z-10">
              Doanh thu tổng
            </div>
          )}
          <RevenueChart data={revenueData} isLoading={isLoading} />
        </div>
        {/* <OrderStatusChart data={orderStatusData} /> */}
      </div>
    </div>
  );
}
