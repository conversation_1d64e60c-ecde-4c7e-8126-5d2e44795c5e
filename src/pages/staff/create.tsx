import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Form, Input, Select, Button, message, Card } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { slugify } from "@/lib/utils";
import { apiCall, endpoints } from "@/lib/api";

const roles = [
  { value: "sales_admin", label: "Nhân viên bán hàng" },
  { value: "sales_manager", label: "Quản lý" },
  { value: "delivery_staff", label: "Nhân viên giao hàng" },
  { value: "warehouse_staff", label: "Nhân viên kho" },
];

const StaffCreate: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      const username = slugify(values.first_name);
      const email = values.email || `${username}@example.com`;

      const payload = {
        username,
        email,
        password: values.password,
        password2: values.password,
        first_name: values.first_name,
        last_name: "",
        role: values.role,
      };

      await apiCall("POST", endpoints.staff.create, payload);

      message.success("Tạo nhân viên mới thành công");
      navigate("/staff");
    } catch (error: any) {
      message.error(error.response?.data?.message || "Có lỗi xảy ra");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate("/staff")}
            type="text"
          >
            Trở về
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">Thêm nhân viên mới</h1>
        </div>
      </div>

      <div className="flex justify-center">
        <Card className="w-full max-w-md">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            className="w-full"
          >
            <Form.Item
              label="Họ tên"
              name="first_name"
              rules={[{ required: true, message: "Vui lòng nhập họ tên" }]}
            >
              <Input placeholder="Nhập họ tên nhân viên" />
            </Form.Item>

            <Form.Item label="Email (tùy chọn)" name="email">
              <Input placeholder="Nhập email" />
            </Form.Item>

            <Form.Item
              label="Mật khẩu"
              name="password"
              rules={[
                { required: true, message: "Vui lòng nhập mật khẩu" },
                { min: 8, message: "Mật khẩu phải có ít nhất 8 ký tự" },
                {
                  pattern: /[a-zA-Z]/,
                  message:
                    "Mật khẩu không được chứa toàn số, phải có ít nhất 1 chữ cái",
                },
                {
                  validator: async (_, value) => {
                    if (!value) return;

                    // Check for common passwords
                    const commonPasswords = [
                      "password123",
                      "12345678",
                      "qwerty",
                    ];
                    if (commonPasswords.includes(value.toLowerCase())) {
                      throw new Error(
                        "Không được sử dụng mật khẩu phổ biến/dễ đoán"
                      );
                    }

                    // Check similarity with user attributes
                    const firstName = form.getFieldValue("first_name");
                    const email = form.getFieldValue("email");
                    const username = firstName ? slugify(firstName) : "";

                    const userAttributes = [username, firstName, email].filter(
                      Boolean
                    );
                    for (const attr of userAttributes) {
                      const attrLower = attr.toLowerCase();
                      const valueLower = value.toLowerCase();
                      if (
                        valueLower.includes(attrLower) ||
                        attrLower.includes(valueLower)
                      ) {
                        throw new Error(
                          "Mật khẩu không được giống với username, first_name hoặc email"
                        );
                      }
                    }
                  },
                },
              ]}
            >
              <Input.Password placeholder="Nhập mật khẩu" />
            </Form.Item>

            <Form.Item
              label="Vai trò"
              name="role"
              rules={[{ required: true, message: "Vui lòng chọn vai trò" }]}
            >
              <Select options={roles} placeholder="Chọn vai trò" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                Tạo nhân viên
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default StaffCreate;
