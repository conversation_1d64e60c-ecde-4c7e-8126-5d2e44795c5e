import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Button } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { apiCall, endpoints } from "@/lib/api";
import StaffTable from "@/components/staff/StaffTable";
import { useAuth } from "@/context/auth-hooks";

interface StaffListItem {
  id: number;
  email: string;
  full_name: string;
  phone_number?: string;
  date_joined: string;
  last_login?: string;
  is_active: boolean;
  profile: {
    role: string;
  };
}

interface StaffSearchParams {
  query?: string;
  searchBy?: "email" | "name";
}

const StaffPage = () => {
  const [page, setPage] = useState(1);
  const [searchParams, setSearchParams] = useState<StaffSearchParams>({});
  const navigate = useNavigate();
  const { user } = useAuth();

  const { data, isLoading } = useQuery<{
    results: StaffListItem[];
    count: number;
  }>({
    queryKey: ["staff", page, searchParams],
    queryFn: () => {
      let url = `${endpoints.staff.list}?page=${page}&page_size=10&exclude_role=customer`;

      if (searchParams.query && searchParams.searchBy) {
        url += `&search=${searchParams.query}&search_by=${searchParams.searchBy}`;
      }

      return apiCall("GET", url);
    },
  });

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Nhân Viên</h1>
        {user?.role === "sales_manager" && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="large"
            onClick={() => navigate("/staff/create")}
          >
            Thêm nhân viên
          </Button>
        )}
      </div>

      <StaffTable
        data={data?.results || []}
        loading={isLoading}
        pagination={{
          current: page,
          pageSize: 10,
          total: data?.count || 0,
          onChange: setPage,
        }}
      />
    </div>
  );
};

export default StaffPage;
