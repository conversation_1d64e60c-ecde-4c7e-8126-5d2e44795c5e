import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button, message } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { apiCall, endpoints } from "@/lib/api";
import { Category, Product } from "@/types/product";
import { CustomUploadFile } from "@/types/upload";
import ProductForm, {
  ProductFormData,
} from "@/components/products/ProductForm";

const CreateProduct = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categories = await apiCall<Category[]>(
          "GET",
          endpoints.categories.list
        );
        setCategories(categories);
      } catch (error) {
        message.error("Không thể tải danh sách danh mục");
      }
    };
    loadCategories();
  }, []);

  const handleSubmit = async (
    values: ProductFormData,
    images: CustomUploadFile[]
  ) => {
    setLoading(true);
    try {
      // Create product
      const product = await apiCall<Product>(
        "POST",
        endpoints.products.create,
        values
      );

      // Upload images
      const uploadPromises = images.map(async (file) => {
        const formData = new FormData();
        formData.append("product", String(product.id));
        formData.append("image", file.originFileObj as File);
        formData.append("alt_text", file.name);
        formData.append("is_primary", String(file.is_primary || false));
        formData.append("sort_order", "0");

        return apiCall("POST", endpoints.products.addImages, formData, {
          isFormData: true,
        });
      });

      await Promise.all(uploadPromises);

      message.success("Tạo sản phẩm thành công");
      navigate("/products");
    } catch (error) {
      message.error("Không thể tạo sản phẩm");
    }
    setLoading(false);
  };

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate("/products")}
            type="text"
          >
            Trở về
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">Thêm sản phẩm mới</h1>
        </div>
      </div>

      <div className="bg-white rounded-lg p-6 shadow-sm">
        <ProductForm
          onFinish={handleSubmit}
          categories={categories}
          loading={loading}
          submitText="Tạo sản phẩm"
        />
      </div>
    </div>
  );
};

export default CreateProduct;
