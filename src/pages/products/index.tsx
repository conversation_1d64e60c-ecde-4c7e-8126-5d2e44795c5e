import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button, Pagination, Spin, message } from "antd";
import { apiCall, endpoints } from "../../lib/api";
import { ProductListItem } from "../../types/product";
import {
  ProductSearch,
  ProductSearchParams,
} from "../../components/products/ProductSearch";
import { useToast } from "../../context/toast-hooks";
import { useProductPermissions } from "../../hooks/useProductPermissions";
import { ProductTable } from "../../components/products/ProductTable";

export default function ProductsPage() {
  const { canManageProducts } = useProductPermissions();
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [searchParams, setSearchParams] = useState<ProductSearchParams>({
    categoryId: null,
  });

  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const { data, isLoading } = useQuery<{
    results: ProductListItem[];
    count: number;
  }>({
    queryKey: ["products", page, searchParams],
    queryFn: () => {
      let url = `${endpoints.products.list}?page=${page}&page_size=10`;

      if (searchParams.query) {
        url += `&search=${searchParams.query}`;
      }

      if (searchParams.categoryId) {
        url += `&category=${searchParams.categoryId}`;
      }

      if (searchParams.inStock) {
        url += "&in_stock=true";
      }

      if (searchParams.isActive !== undefined) {
        url += `&is_active=${searchParams.isActive}`;
      }

      return apiCall("GET", url);
    },
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Sản phẩm</h1>
        {canManageProducts && (
          <Button
            type="primary"
            size="large"
            onClick={() => navigate("/products/create")}
          >
            Thêm sản phẩm
          </Button>
        )}
      </div>

      <ProductSearch onSearch={setSearchParams} />

      <ProductTable data={data?.results || []} loading={isLoading} />

      {data && data.results.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          Không tìm thấy sản phẩm phù hợp.
        </div>
      )}

      {data && data.results.length > 0 && (
        <Pagination
          align="center"
          current={page}
          total={data.count}
          pageSize={10}
          onChange={setPage}
          showSizeChanger={false}
        />
      )}
    </div>
  );
}
