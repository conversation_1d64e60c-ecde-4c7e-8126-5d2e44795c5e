import { useEffect, useState } from "react";
import { useProductPermissions } from "../../hooks/useProductPermissions";
import { Button, message, Image } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { apiCall, endpoints } from "@/lib/api";
import { Product, Category } from "@/types/product";
import { CustomUploadFile } from "@/types/upload";
import ProductForm, {
  ProductFormData,
} from "@/components/products/ProductForm";

const ProductDetail = () => {
  const { canManageProducts } = useProductPermissions();
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [productData, setProductData] = useState<Product | null>(null);

  useEffect(() => {
    // Load product details
    const loadProduct = async () => {
      setLoading(true);
      try {
        const product = await apiCall<Product>(
          "GET",
          endpoints.products.detail(Number(id))
        );
        setProductData(product);
      } catch (error) {
        message.error("Không thể tải thông tin sản phẩm");
      }
      setLoading(false);
    };

    // Load categories
    const loadCategories = async () => {
      try {
        const categories = await apiCall<Category[]>(
          "GET",
          endpoints.categories.list
        );
        setCategories(categories);
      } catch (error) {
        message.error("Không thể tải danh sách danh mục");
      }
    };

    loadProduct();
    loadCategories();
  }, [id]);

  const handleSubmit = async (
    values: ProductFormData,
    images: CustomUploadFile[]
  ) => {
    setLoading(true);
    try {
      // Update product
      await apiCall("PUT", endpoints.products.update(Number(id)), {
        ...values,
        category: values.category || null,
      });

      // Handle images
      const existingImages = productData?.images || [];
      const updatedImages = images.filter((img) => !img.url); // Only process new images

      // Upload new images
      const uploadPromises = updatedImages.map(async (file) => {
        const formData = new FormData();
        formData.append("product", String(id));
        formData.append("image", file.originFileObj as File);
        formData.append("alt_text", file.name);
        formData.append("is_primary", String(file.is_primary || false));
        formData.append("sort_order", "0");

        return apiCall("POST", endpoints.products.addImages, formData, {
          isFormData: true,
        });
      });

      await Promise.all(uploadPromises);

      message.success("Cập nhật sản phẩm thành công");

      // Reload product data to get new images
      const updatedProduct = await apiCall<Product>(
        "GET",
        endpoints.products.detail(Number(id))
      );
      setProductData(updatedProduct);
    } catch (error) {
      message.error("Không thể cập nhật sản phẩm");
    }
    setLoading(false);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate("/products")}
            type="text"
          >
            Trở về
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">
            Chi tiết sản phẩm #{id}
          </h1>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="font-medium mb-4">Hình ảnh sản phẩm</h2>
          <div className="space-y-4">
            {productData?.main_image && (
              <div className="aspect-square w-full relative rounded-lg overflow-hidden border border-gray-200">
                <Image
                  src={productData.main_image}
                  alt={productData.name}
                  className="object-contain"
                  width="100%"
                  height="100%"
                />
              </div>
            )}
            {productData?.images && productData.images.length > 0 && (
              <>
                <h3 className="font-medium text-sm text-gray-500 mt-6 mb-2">
                  Thư viện ảnh
                </h3>
                <div className="grid grid-cols-3 gap-3">
                  {productData.images.map((img) => (
                    <div
                      key={img.id}
                      className="aspect-square relative rounded-lg overflow-hidden border border-gray-200"
                    >
                      <Image
                        src={img.image}
                        alt={img.alt_text || ""}
                        className="object-contain"
                        width="100%"
                        height="100%"
                        preview={{
                          mask: (
                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white">
                              <span>Xem</span>
                            </div>
                          ),
                        }}
                      />
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
        <div className="md:col-span-2 bg-white rounded-lg p-6 shadow-sm">
          <ProductForm
            onFinish={handleSubmit}
            initialValues={
              productData
                ? {
                    name: productData.name,
                    description: productData.description,
                    price: productData.price,
                    discount_price: productData.discount_price,
                    stock: productData.stock,
                    weight: productData.weight,
                    unit: productData.unit,
                    code: productData.code,
                    specifications: productData.specifications,
                    is_featured: productData.is_featured,
                    is_active: productData.is_active,
                    category: productData.category,
                  }
                : undefined
            }
            categories={categories}
            loading={loading}
            existingImages={productData?.images}
            disabled={!canManageProducts}
          />
          {!canManageProducts && (
            <div className="mt-4 text-center text-gray-500">
              Chỉ quản lý bán hàng mới có quyền chỉnh sửa sản phẩm.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
