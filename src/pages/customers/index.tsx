import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Pagination, Space } from "antd";
import { Bar<PERSON>hartOutlined } from "@ant-design/icons";
import { Staff } from "../../types/staff";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiCall, endpoints } from "../../lib/api";
import { CustomerListItem } from "../../types/customer";
import {
  CustomerSearch,
  CustomerSearchParams,
} from "../../components/customers/CustomerSearch";
import { CustomerTable } from "../../components/customers/CustomerTable";
import { useToast } from "../../context/toast-hooks";
import { useAuth } from "../../context/auth-hooks";
import { StaffFilter } from "../../components/orders/StaffFilter";

export default function CustomersPage() {
  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<number | null>(
    null
  );
  const [staffList, setStaffList] = useState<Staff[]>([]);
  const [page, setPage] = useState(1);
  const [searchParams, setSearchParams] = useState<CustomerSearchParams>({
    searchBy: "email",
  });

  const { user } = useAuth();

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  useEffect(() => {
    const fetchStaffList = async () => {
      try {
        const response = await apiCall<{ results: Staff[] }>(
          "GET",
          endpoints.staff.list + `?is_staff=true&no_page=true`
        );
        setStaffList(response.results);
      } catch (error) {
        console.error("Failed to fetch staff list:", error);
      }
    };
    fetchStaffList();
  }, []);

  const { data, isLoading } = useQuery<{
    results: CustomerListItem[];
    count: number;
  }>({
    queryKey: ["customers", page, searchParams, selectedSalesAdmin],
    queryFn: () => {
      let url = `${endpoints.customers.list}?page=${page}&page_size=10&needdetail=true&role=customer`;

      if (user?.role === "sales_admin") {
        url += `&creator_id=${user.id}`;
      } else if (selectedSalesAdmin) {
        url += `&creator_id=${selectedSalesAdmin}`;
      }

      if (searchParams.query) {
        url += `&search=${searchParams.query}&search_by=${searchParams.searchBy}`;
      }

      if (searchParams.dateFrom) {
        url += `&date_from=${searchParams.dateFrom}`;
      }

      if (searchParams.dateTo) {
        url += `&date_to=${searchParams.dateTo}`;
      }

      if (searchParams.status) {
        url += `&is_active=${searchParams.status === "active"}`;
      }

      if (searchParams.hasOrders) {
        url += "&has_orders=true";
      }

      return apiCall("GET", url);
    },
  });

  const toggleCustomerStatus = async (
    customerId: number,
    isActive: boolean
  ) => {
    try {
      await apiCall("PATCH", endpoints.customers.update(customerId), {
        is_active: !isActive,
      });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
      showToast(
        `Khách hàng đã được ${isActive ? "vô hiệu hóa" : "kích hoạt"}`,
        "success"
      );
    } catch (error) {
      console.error("Không thể cập nhật trạng thái khách hàng:", error);
      showToast("Không thể cập nhật trạng thái khách hàng", "error");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Khách hàng</h1>
        <Space>
          <Button
            size="large"
            icon={<BarChartOutlined />}
            onClick={() => navigate("/reports/top-customers")}
          >
            Báo cáo Top khách hàng
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={() => navigate("/customers/create")}
          >
            Tạo khách hàng
          </Button>
        </Space>
      </div>

      <CustomerSearch onSearch={setSearchParams} />

      {user?.role !== "sales_admin" && (
        <StaffFilter
          userRole={user?.role || ""}
          salesAdmin={staffList.find(
            (staff) => staff.id === selectedSalesAdmin
          )}
          onSalesAdminChange={setSelectedSalesAdmin}
          onDeliveryStaffChange={() => {}} // Empty function since we don't need delivery staff filtering
          staffList={staffList}
        />
      )}

      <CustomerTable
        data={data?.results || []}
        loading={isLoading}
        onToggleStatus={toggleCustomerStatus}
      />

      {data && data.results.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          Không tìm thấy khách hàng phù hợp.
        </div>
      )}

      {data && data.results.length > 0 && (
        <Pagination
          align="center"
          current={page}
          total={data.count}
          pageSize={10}
          onChange={setPage}
          showSizeChanger={false}
        />
      )}
    </div>
  );
}
