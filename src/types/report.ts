export interface DashboardStats {
  // Base statistics
  total_revenue: number;
  total_orders: number;
  average_order_value: number;
  pending_orders: number;
  // Revenue data by date
  revenue_by_date: Array<{
    date: string;
    total_revenue: number;
  }>;
  // Order counts by status
  orders_by_status: Record<string, number>;
}

export interface ProductRevenueItem {
  id: number;
  code: string;
  name: string;
  category: number | null;
  price: number;
  discount_price: number | null;
  total_quantity_sold: number;
  total_revenue: number;
  is_active: boolean;
  stock: number;
}

export interface ProductRevenueResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ProductRevenueItem[];
}

export interface RevenueComparison {
  period: string;
  revenue: number;
  totalProducts: number;
}

export interface DeliveryRevenueItem {
  type: "delivery_staff" | "shipping_method";
  name: string;
  revenue: number;
  orders: number;
}

export interface DeliveryRevenueResponse {
  delivery_revenue: DeliveryRevenueItem[];
}

export interface TopCustomerItem {
  user_id: number;
  user_name: string;
  email: string;
  role: string;
  total_orders: number;
  total_spent: number;
  last_order_date?: string;
  rank?: number;
}

export interface TopCustomerResponse {
  user_stats: TopCustomerItem[];
}

export type OrderByOption =
  | "total_spent_desc"
  | "total_spent_asc"
  | "total_orders_desc"
  | "total_orders_asc";

export type LimitOption =
  | "all"
  | "top10"
  | "top20"
  | "top50"
  | string; // for custom numbers like "5", "15", "100"
