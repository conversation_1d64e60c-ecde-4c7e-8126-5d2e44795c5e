export interface UserProfile {
  phone_number: string | null;
  shipping_address: string;
  ward: string | null;
  district: string | null;
  city: string | null;
  role: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone_number: string | null;
  shipping_address: string;
  ward: string | null;
  district: string | null;
  city: string | null;
  role: string;
  is_staff: boolean;
  is_superuser: boolean;
  profile: UserProfile;
  active: boolean;
}

export interface MeResponse {
  user: User;
}

export interface AuthResponse {
  access: string;
  refresh: string;
}
