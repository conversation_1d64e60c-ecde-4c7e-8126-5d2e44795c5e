export interface CustomerProfile {
  phone_number?: string | null;
  shipping_address?: string | null;
  ward?: string | null;
  district?: string | null;
  city?: string | null;
}

export interface Customer {
  id: number;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  date_joined?: string;
  last_login?: string;
  is_active: boolean;
  role?: 'sales_admin' | 'sales_manager' | 'bod' | 'delivery_staff' | 'warehouse_staff' | 'accounting_staff' | 'accountant' | 'customer';
  is_staff?: boolean;
  is_superuser?: boolean;
  profile: CustomerProfile;
}

export interface CustomerListItem {
  id: number;
  email: string;
  username: string;
  full_name: string;
  date_joined: string;
  last_login?: string;
  is_active: boolean;
  phone_number?: string;
  total_orders: number;
  total_spent: number;
}

export interface CustomerStats {
  total_spent: number;
  order_count: number;
  last_order_date?: string;
  avg_order_value: number;
  favorite_categories: Array<{
    category: string;
    order_count: number;
  }>;
}

export interface CustomerOrderSummary {
  id: number;
  created_at: string;
  total_price: number;
  status: string;
  items_count: number;
}

export interface CustomerNote {
  id: number;
  customer_id: number;
  content: string;
  created_at: string;
  created_by: {
    id: number;
    email: string;
    full_name: string;
  };
  updated_at?: string;
  is_pinned: boolean;
}

export interface CreateNoteData {
  content: string;
  is_pinned?: boolean;
}

export interface CreateCustomerData {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  creator_id: number;
}

export interface CreatedCustomerResponse {
  user: Customer & {
    password: string;
  };
}
