export interface Category {
  id: number;
  name: string;
  description?: string;
  parent?: Category;
  image?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductVariant {
  id: number;
  name: string;
  sku?: string;
  price?: number;
  discount_price?: number;
  stock: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  images: VariantImage[];
}

export interface VariantImage {
  id: number;
  image: string;
  alt_text?: string;
  is_primary: boolean;
  sort_order: number;
}

export interface ProductImage {
  id: number;
  image: string;
  alt_text?: string;
  is_primary: boolean;
  sort_order: number;
}

export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  weight?: number;
  unit?: string;
  specifications?: string;
  discount_price?: number;
  category?: number;
  stock: number;
  image: string;
  images: ProductImage[];
  is_featured: boolean;
  is_active: boolean;
  code?: string;
  created_at: string;
  updated_at: string;
  variants: ProductVariant[];
  main_image: string;
}

export interface ProductListItem {
  id: number;
  name: string;
  price: number;
  discount_price?: number;
  category?: number;
  stock: number;
  main_image: string;
  image: string;
  is_active: boolean;
  code?: string;
  variant_count: number;
  category_name?: string;
}
