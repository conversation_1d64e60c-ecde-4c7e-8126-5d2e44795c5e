import { AxiosError } from "axios";

export type ApiErrorResponse = {
  data: string[];
  status: number;
  statusText: string;
};

export type ApiError = AxiosError<ApiErrorResponse>;

export const isApiError = (error: unknown): error is ApiError & { response: { data: string[] } } => {
  return (
    error != null &&
    typeof error === "object" &&
    "response" in error &&
    error.response != null &&
    typeof error.response === "object" &&
    "data" in error.response &&
    Array.isArray(error.response.data)
  );
};
