{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@8.7.1", "scripts": {"dev": "vite", "build": "npx vite build", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.72.2", "@tanstack/react-query-devtools": "^5.72.2", "@types/file-saver": "^2.0.7", "antd": "^5.24.8", "axios": "^1.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "lucide-react": "^0.314.0", "moment": "^2.30.1", "papaparse": "^5.5.2", "react": "^18.3.1", "react-day-picker": "^9.6.5", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-router-dom": "^6.30.0", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.24.0", "@types/node": "^20.17.30", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.19", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "typescript-eslint": "^8.29.1", "vite": "^6.2.6"}}