# Refactor Customers Page - Đồng bộ giao diện với các trang index khác

## Mục tiêu
Refactor trang customers/index.tsx và CustomerSearch component để đồng bộ với pattern chung của các trang index khác trong dự án.

## Checklist

### ✅ Hoàn thành - Refactor customers/index.tsx
- [x] Cập nhật imports để sử dụng Ant Design components
- [x] Thay thế custom button bằng Ant Design Button component
- [x] Cập nhật header layout với Button size="large"
- [x] Tạo CustomerTable component để thay thế HTML table
- [x] Cập nhật Pagination để sử dụng Ant Design Pagination
- [x] Loại bỏ loading state riêng biệt (để loading được handle trong table)
- [x] Đồng bộ cấu trúc layout với `space-y-6`

### ✅ Hoàn thành - Tạo CustomerTable component
- [x] Tạo file src/components/customers/CustomerTable.tsx
- [x] Sử dụng Ant Design Table component
- [x] Implement columns với proper styling
- [x] Thêm loading state
- [x] Thêm onToggleStatus prop (sẵn sàng cho tương lai)
- [x] Consistent row styling với các table khác

### ✅ Hoàn thành - Refactor CustomerSearch component
- [x] Cập nhật imports để sử dụng Ant Design components
- [x] Thay thế HTML inputs bằng Ant Design Input, Select
- [x] Thay thế date inputs bằng DateRangePickerWithPresets
- [x] Cập nhật buttons với Ant Design Button và icons
- [x] Thay thế HTML checkbox bằng Ant Design Checkbox
- [x] Loại bỏ form submission, sử dụng onClick handlers
- [x] Cập nhật layout và styling để đồng bộ với các search components khác
- [x] Thêm size="large" cho consistency
- [x] Thêm allowClear và onPressEnter cho Input

## Thay đổi chi tiết

### customers/index.tsx
- **Imports**: Thêm Button, Pagination từ antd
- **Header**: Sử dụng Ant Design Button thay vì custom button
- **Table**: Thay thế HTML table bằng CustomerTable component
- **Pagination**: Sử dụng Ant Design Pagination với align="center"
- **Loading**: Loại bỏ separate loading state, để table handle loading

### CustomerTable.tsx (Mới)
- **Structure**: Sử dụng Ant Design Table với proper columns
- **Styling**: Consistent với ProductTable, OrderTable patterns
- **Features**: Loading state, row styling, action buttons
- **Props**: data, loading, onToggleStatus

### CustomerSearch.tsx
- **Components**: Thay thế tất cả HTML elements bằng Ant Design components
- **Layout**: Cập nhật để match với ProductSearch, OrderSearch patterns
- **Date Range**: Sử dụng DateRangePickerWithPresets thay vì separate date inputs
- **Buttons**: Thêm icons (SearchOutlined, ReloadOutlined)
- **Interaction**: Auto-search khi thay đổi date range, onPressEnter support

## Kết quả
- ✅ Giao diện customers page đã đồng bộ với các trang index khác
- ✅ Sử dụng consistent Ant Design components
- ✅ Layout và styling patterns đồng nhất
- ✅ Better UX với auto-search và keyboard support
- ✅ Code structure cleaner và maintainable hơn

## Testing
- [ ] Test search functionality
- [ ] Test pagination
- [ ] Test responsive layout
- [ ] Test loading states
- [ ] Verify build passes
