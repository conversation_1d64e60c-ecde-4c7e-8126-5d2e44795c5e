# Thêm nút navigate tới trang báo cáo Top Customers

## Mục tiêu
Thêm các nút navigation để người dùng có thể dễ dàng truy cập trang báo cáo Top Customers từ các trang liên quan.

## Checklist

### ✅ Hoàn thành - Thêm nút vào trang Customers
- [x] Import BarChartOutlined icon và Space component từ antd
- [x] Thêm nút "Báo cáo Top khách hàng" vào header của trang customers
- [x] Sử dụng Space component để group buttons
- [x] Navigate tới "/reports/top-customers" khi click
- [x] Styling consistent với nút "Tạo khách hàng"

### ✅ Hoàn thành - Thêm nút vào trang Orders (chỉ cho sales_manager)
- [x] Import BarChartOutlined icon từ antd
- [x] Thêm nút "Báo cáo Top khách hàng" vào section sales_manager
- [x] Đặt trước các nút Export/Import để ưu tiên
- [x] Navigate tới "/reports/top-customers" khi click
- [x] Chỉ hiển thị cho user có role "sales_manager"

## Thay đổi chi tiết

### src/pages/customers/index.tsx
```tsx
// Before
<div className="flex justify-between items-center">
  <h1 className="text-3xl font-bold">Khách hàng</h1>
  <Button type="primary" size="large" onClick={() => navigate("/customers/create")}>
    Tạo khách hàng
  </Button>
</div>

// After  
<div className="flex justify-between items-center">
  <h1 className="text-3xl font-bold">Khách hàng</h1>
  <Space>
    <Button size="large" icon={<BarChartOutlined />} onClick={() => navigate("/reports/top-customers")}>
      Báo cáo Top khách hàng
    </Button>
    <Button type="primary" size="large" onClick={() => navigate("/customers/create")}>
      Tạo khách hàng
    </Button>
  </Space>
</div>
```

### src/pages/orders/index.tsx
```tsx
// Added in sales_manager section
<Button
  size="large"
  icon={<BarChartOutlined />}
  onClick={() => navigate("/reports/top-customers")}
>
  Báo cáo Top khách hàng
</Button>
```

## UX/UI Improvements

### Customers Page
- **Button grouping**: Sử dụng Space component để group buttons đẹp mắt
- **Icon consistency**: Sử dụng BarChartOutlined icon cho báo cáo
- **Priority**: Nút báo cáo đặt trước nút tạo để dễ truy cập
- **Styling**: Size "large" consistent với nút khác

### Orders Page  
- **Permission-based**: Chỉ hiển thị cho sales_manager
- **Logical placement**: Đặt trong group buttons của sales_manager
- **Priority order**: Báo cáo → Export → Import (theo mức độ sử dụng)
- **Consistent styling**: Size và icon pattern giống nhau

## Benefits

### Improved User Experience
- **Quick access**: Không cần vào menu Reports để truy cập báo cáo
- **Contextual**: Từ trang customers có thể nhanh chóng xem báo cáo customers
- **Workflow optimization**: Sales manager có thể xem báo cáo ngay từ trang orders
- **Discoverability**: Người dùng dễ dàng phát hiện tính năng báo cáo

### Business Value
- **Increased usage**: Báo cáo được sử dụng nhiều hơn khi dễ truy cập
- **Better insights**: Sales team có thể nhanh chóng phân tích top customers
- **Time saving**: Giảm số click cần thiết để truy cập báo cáo
- **Data-driven decisions**: Khuyến khích việc sử dụng data để ra quyết định

## Navigation Paths

### Từ Customers Page
1. **Trang chủ** → **Khách hàng** → **Báo cáo Top khách hàng** ✨
2. **Trang chủ** → **Phân tích** → **Top khách hàng** (existing)

### Từ Orders Page (sales_manager only)
1. **Trang chủ** → **Đơn hàng** → **Báo cáo Top khách hàng** ✨
2. **Trang chủ** → **Phân tích** → **Top khách hàng** (existing)

## Files Modified
- **src/pages/customers/index.tsx**: Thêm navigation button
- **src/pages/orders/index.tsx**: Thêm navigation button cho sales_manager

## Testing Checklist
- [ ] Test navigation từ customers page
- [ ] Test navigation từ orders page (sales_manager)
- [ ] Verify button không hiển thị cho non-sales_manager ở orders page
- [ ] Test responsive design với buttons mới
- [ ] Verify styling consistency
- [ ] Test accessibility (keyboard navigation, screen readers)

## Kết quả
- ✅ Navigation buttons đã được thêm vào 2 trang chính
- ✅ UX được cải thiện với quick access
- ✅ Permission-based display hoạt động đúng
- ✅ Styling consistent và đẹp mắt
- ✅ Code clean và maintainable
