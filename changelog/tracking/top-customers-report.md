# Tạo trang báo cáo Top Customers - <PERSON><PERSON><PERSON><PERSON> hàng có doanh thu cao nhất

## M<PERSON><PERSON> tiêu

Tạo trang báo cáo mới để hiển thị danh sách khách hàng có doanh thu cao nhất với các tính năng filter và export Excel.

## Checklist

### ✅ Hoàn thành - T<PERSON><PERSON> types cho Top Customer Report

- [x] Thêm TopCustomerItem interface vào src/types/report.ts
- [x] Thêm TopCustomerResponse interface
- [x] Định nghĩa các fields: id, full_name, email, phone_number, total_revenue, total_orders, average_order_value, last_order_date, rank

### ✅ Hoàn thành - Tạo trang báo cáo Top Customers

- [x] Tạo file src/pages/reports/top-customers.tsx
- [x] Implement filters: DateRangePickerWithPresets, InputNumber cho top limit
- [x] Tạo bảng hiển thị data với columns: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON> doanh thu, <PERSON><PERSON> đơn hàng, <PERSON><PERSON><PERSON> trị TB/đơn, <PERSON><PERSON><PERSON> hàng cuối
- [x] Thêm styling đặc biệt cho top 3 (trophy icons, background colors)
- [x] Implement loading states và error handling
- [x] Sử dụng API endpoint /reports/top_customers/

### ✅ Hoàn thành - Tính năng Export Excel

- [x] Cài đặt thư viện xlsx
- [x] Implement handleExportExcel function
- [x] Tạo Excel file với proper formatting
- [x] Set column widths và filename với date range
- [x] Thêm button Export với icon và disabled state

### ✅ Hoàn thành - Routing và Navigation

- [x] Thêm import TopCustomersReport vào src/routes/allRoutes.tsx
- [x] Thêm route "/reports/top-customers" vào routes array
- [x] Thêm menu item vào AdminLayout.tsx trong reports section
- [x] Cập nhật navigationPaths.ts với REPORTS_TOP_CUSTOMERS

## Tính năng chính

### Filters

- **Khoảng thời gian**: Sử dụng DateRangePickerWithPresets với default 30 ngày gần nhất
- **Top limit**: InputNumber để chọn số lượng khách hàng hiển thị (1-100, default 10)
- **Button Tìm kiếm**: Trigger API call với filters mới

### Bảng dữ liệu

- **Hạng**: Hiển thị ranking với trophy icons cho top 3
- **Khách hàng**: Tên, email, số điện thoại với icon
- **Tổng doanh thu**: Format currency với màu xanh lá
- **Số đơn hàng**: Số lượng đơn hàng
- **Giá trị TB/đơn**: Average order value với màu xanh dương
- **Đơn hàng cuối**: Last order date format DD/MM/YYYY

### Styling đặc biệt

- **Top 1**: Background vàng nhạt, text vàng đậm, trophy vàng
- **Top 2**: Background xám nhạt, text xám, trophy xám
- **Top 3**: Background cam nhạt, text cam, trophy cam

### Export Excel

- **Format**: .xlsx file với tên file chứa date range
- **Columns**: Tất cả thông tin từ bảng
- **Styling**: Column widths tự động, proper headers
- **Disabled**: Khi không có data

## API Integration

- **Endpoint**: `/reports/top_customers/`
- **Parameters**:
  - `dateFrom`: YYYY-MM-DD format
  - `dateTo`: YYYY-MM-DD format
  - `limit`: Number (1-100)
- **Response**: TopCustomerResponse với customers array và metadata

## Dependencies

- **xlsx**: ^0.18.5 - Thư viện export Excel
- **dayjs**: Đã có - Date formatting
- **antd**: Đã có - UI components
- **@ant-design/icons**: Đã có - Icons

## Navigation

- **Menu**: Phân tích > Top khách hàng
- **URL**: /reports/top-customers
- **Permission**: Chỉ sales_manager (theo filter hiện tại của reports)

## ✅ Cập nhật mới - Sử dụng endpoint mới với đầy đủ tính năng

### API Endpoint mới

- **Endpoint**: `/reports/top_customers/` (thay vì `/reports/top_customer/`)
- **Response format**: `{ user_stats: [...] }` thay vì `{ customers: [...] }`
- **Data fields**: `user_id`, `user_name`, `total_spent` thay vì `id`, `full_name`, `total_revenue`

### Tính năng mới đã thêm

- **🔄 Sắp xếp linh hoạt**:
  - Doanh thu cao → thấp / thấp → cao
  - Đơn hàng nhiều → ít / ít → nhiều
- **📊 Tùy chọn số lượng**: Top 10/20/50, Tất cả, hoặc Tùy chỉnh (1-1000)
- **👥 Filter theo Role**: Khách hàng, Sales Admin, hoặc Tất cả
- **📅 Filter ngày tháng**: Tùy chọn (không bắt buộc)
- **🎯 UI cải tiến**: Grid layout responsive, controls rõ ràng hơn

### Cập nhật kỹ thuật

- **Types**: Cập nhật TopCustomerItem, TopCustomerResponse, thêm OrderByOption, LimitOption
- **API params**: dateFrom, dateTo, order_by, limit, role
- **Dynamic title**: Hiển thị thông tin filter hiện tại
- **Better UX**: Custom limit input, role selection, sort options

## Testing cần thực hiện

- [ ] Test API call với các filters khác nhau (order_by, limit, role)
- [ ] Test export Excel với data mới
- [ ] Test responsive design với grid layout mới
- [ ] Test loading và error states
- [ ] Test navigation và permissions
- [ ] Verify data formatting và styling với fields mới
- [ ] Test custom limit input
- [ ] Test role filtering

## Kết quả

- ✅ Trang báo cáo hoàn chỉnh với endpoint mới
- ✅ Đầy đủ tính năng filter và sort theo yêu cầu
- ✅ UI/UX đẹp và responsive với grid layout
- ✅ Export Excel hoạt động với data structure mới
- ✅ Navigation menu đã được cập nhật
- ✅ Code structure clean và maintainable
- ✅ Hỗ trợ đầy đủ các tham số của endpoint mới
